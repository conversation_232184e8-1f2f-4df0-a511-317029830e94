<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BIMEX - Interface d'Analyse Intelligente BIM</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-medium: 0 15px 35px rgba(31, 38, 135, 0.2);
            --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
            z-index: -1;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-heavy);
            overflow: hidden;
            border: 1px solid var(--glass-border);
        }

        .header {
            background: var(--dark-gradient);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 30s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .logo-image {
            width: 80px;
            height: 80px;
            border-radius: 16px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .logo-image:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .brand-text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .brand-name {
            font-size: 3.5em;
            font-weight: 800;
            background: linear-gradient(45deg, #fff, #e3f2fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .brand-tagline {
            font-size: 1.3em;
            font-weight: 400;
            opacity: 0.9;
            margin-top: 5px;
            letter-spacing: 0.5px;
        }

        .header-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            padding: 15px 25px;
            background: var(--glass-bg);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
        }

        .stat-number {
            display: block;
            font-size: 2em;
            font-weight: 700;
            color: #fff;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }

        .main-content {
            padding: 40px;
            background: linear-gradient(180deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid rgba(226, 232, 240, 0.8);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.12);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .feature-card h3 {
            color: var(--text-primary);
            margin-bottom: 16px;
            font-size: 1.4em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .feature-card h3 i {
            color: #667eea;
            font-size: 1.2em;
        }

        .feature-card p {
            color: var(--text-secondary);
            margin-bottom: 20px;
            line-height: 1.7;
            font-size: 0.95em;
        }

        .upload-area {
            border: 2px dashed rgba(102, 126, 234, 0.3);
            border-radius: 20px;
            padding: 50px;
            text-align: center;
            background: linear-gradient(145deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
            margin: 30px 0;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
            transition: opacity 0.3s ease;
            opacity: 0;
        }

        .upload-area:hover::before {
            opacity: 1;
        }

        .upload-area:hover {
            border-color: rgba(102, 126, 234, 0.6);
            background: linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(240,245,255,0.95) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .upload-area.dragover {
            border-color: #10b981;
            background: linear-gradient(145deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.1) 100%);
            transform: scale(1.02);
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: var(--primary-gradient);
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 12px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .upload-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .action-btn {
            background: var(--success-gradient);
            color: white;
            padding: 14px 28px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 8px;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        }

        .action-btn:disabled {
            background: linear-gradient(135deg, #cbd5e0, #a0aec0);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.6;
        }

        .action-btn:disabled::before {
            display: none;
        }

        .results-area {
            margin-top: 40px;
            padding: 0;
            background: transparent;
            border-radius: 20px;
            display: none;
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .results-area.show {
            display: block;
        }

        .loading {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(102, 126, 234, 0.1);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .metric-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            padding: 24px;
            border-radius: 16px;
            margin: 16px 0;
            border: 1px solid rgba(226, 232, 240, 0.8);
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            font-weight: 600;
        }

        .anomaly-item {
            background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%);
            padding: 20px;
            border-radius: 12px;
            margin: 12px 0;
            border-left: 4px solid #e74c3c;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
        }

        .anomaly-item:hover {
            transform: translateX(4px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
        }

        .anomaly-critical {
            border-left-color: #c0392b;
            background: linear-gradient(145deg, #fff5f5 0%, #ffffff 100%);
        }
        .anomaly-high {
            border-left-color: #e67e22;
            background: linear-gradient(145deg, #fffaf0 0%, #ffffff 100%);
        }
        .anomaly-medium {
            border-left-color: #f39c12;
            background: linear-gradient(145deg, #fffbf0 0%, #ffffff 100%);
        }
        .anomaly-low {
            border-left-color: #95a5a6;
            background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
        }

        .chat-area {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 24px;
            margin-top: 24px;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid rgba(226, 232, 240, 0.8);
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            backdrop-filter: blur(10px);
        }

        .chat-message {
            margin: 16px 0;
            padding: 16px 20px;
            border-radius: 16px;
            animation: messageSlide 0.3s ease-out;
            position: relative;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-message.user {
            background: var(--primary-gradient);
            color: white;
            text-align: right;
            margin-left: 60px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .chat-message.assistant {
            background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
            color: var(--text-primary);
            margin-right: 60px;
            border: 1px solid rgba(226, 232, 240, 0.8);
        }

        .chat-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid rgba(226, 232, 240, 0.8);
            border-radius: 50px;
            font-size: 1em;
            margin-top: 16px;
            outline: none;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .chat-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .tabs {
            display: flex;
            background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 16px 16px 0 0;
            overflow: hidden;
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-bottom: none;
        }

        .tab {
            flex: 1;
            padding: 18px 24px;
            text-align: center;
            cursor: pointer;
            background: transparent;
            border: none;
            font-size: 1em;
            font-weight: 500;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            position: relative;
        }

        .tab::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .tab.active {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            color: var(--text-primary);
            font-weight: 600;
        }

        .tab.active::after {
            transform: scaleX(1);
        }

        .tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.5);
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
            padding: 30px;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 0 0 16px 16px;
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-top: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeInUp 0.4s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
            margin-right: 8px;
        }

        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #d97706;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }
        .status-error {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        .status-info {
            background: rgba(59, 130, 246, 0.1);
            color: #2563eb;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        /* Progress Bar Styles */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(226, 232, 240, 0.5);
            border-radius: 50px;
            overflow: hidden;
            margin: 24px 0;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            width: 0%;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 50px;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 16px;
            }

            .header {
                padding: 30px 20px;
            }

            .brand-name {
                font-size: 2.5em;
            }

            .header-stats {
                gap: 20px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .main-content {
                padding: 24px;
            }

            .chat-message.user {
                margin-left: 20px;
            }

            .chat-message.assistant {
                margin-right: 20px;
            }

            .tabs {
                flex-direction: column;
            }

            .tab {
                border-radius: 0;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .logo-container {
                flex-direction: column;
                gap: 15px;
            }

            .brand-name {
                font-size: 2em;
            }

            .header-stats {
                flex-direction: column;
                gap: 15px;
            }
        }

        /* 🎨 STYLES POUR LES NOUVELLES FONCTIONNALITÉS DATA SCIENCE */

        .cost-breakdown {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .breakdown-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            font-size: 0.9em;
        }

        .recommendations-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .recommendation-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
            font-size: 0.9em;
        }

        .sensitivity-chart {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .sensitivity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 0.9em;
        }

        .sensitivity-item span:first-child {
            min-width: 120px;
            font-weight: 500;
        }

        .impact-bar {
            flex: 1;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .impact-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .recommendations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .env-recommendation {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .rec-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .rec-header h5 {
            margin: 0;
            color: var(--text-primary);
            font-size: 1.1em;
        }

        .difficulty-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .difficulty-badge.easy {
            background: #d1fae5;
            color: #065f46;
        }

        .difficulty-badge.medium {
            background: #fef3c7;
            color: #92400e;
        }

        .difficulty-badge.hard {
            background: #fecaca;
            color: #991b1b;
        }

        .rec-metrics {
            display: flex;
            gap: 15px;
            margin-top: 10px;
            font-size: 0.85em;
            color: var(--text-secondary);
        }

        .standards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .standard-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 8px;
        }

        .standard-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .compliance-status.compliant {
            color: #059669;
            font-weight: 500;
        }

        .compliance-status.non-compliant {
            color: #dc2626;
            font-weight: 500;
        }

        .standard-score {
            font-weight: 600;
            color: var(--text-primary);
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .category-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .category-card h5 {
            margin: 0 0 10px 0;
            color: var(--text-primary);
            font-size: 1.1em;
        }

        .category-card p {
            margin: 5px 0;
            font-size: 0.9em;
            color: var(--text-secondary);
        }

        .roadmap-timeline {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .roadmap-phase {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .phase-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
        }

        .phase-header h5 {
            margin: 0;
            color: var(--text-primary);
            font-size: 1.2em;
        }

        .phase-duration {
            background: var(--primary-gradient);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .phase-content ul {
            margin: 0 0 15px 0;
            padding-left: 20px;
        }

        .phase-content li {
            margin: 5px 0;
            color: var(--text-secondary);
        }

        .phase-cost {
            text-align: right;
            color: var(--text-primary);
        }

        .priority-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .priority-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .priority-rank {
            background: var(--primary-gradient);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }

        .priority-content {
            flex: 1;
        }

        .priority-content h6 {
            margin: 0 0 10px 0;
            color: var(--text-primary);
            font-size: 1.1em;
        }

        .priority-metrics {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
        }

        .impact-score {
            background: #dbeafe;
            color: #1e40af;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .difficulty.easy {
            background: #d1fae5;
            color: #065f46;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .difficulty.medium {
            background: #fef3c7;
            color: #92400e;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .difficulty.hard {
            background: #fecaca;
            color: #991b1b;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .priority.high {
            background: #fecaca;
            color: #991b1b;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .priority.medium {
            background: #fef3c7;
            color: #92400e;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .priority.low {
            background: #d1fae5;
            color: #065f46;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .priority-benefits {
            display: flex;
            gap: 15px;
            font-size: 0.85em;
            color: var(--text-secondary);
        }

        .priority-benefits span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* 🎨 STYLES POUR LES POP-UPS MODERNES */

        .modern-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transform: scale(0.9);
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .modern-popup.show {
            opacity: 1;
            transform: scale(1);
        }

        .modern-popup.hide {
            opacity: 0;
            transform: scale(0.9);
        }

        .popup-content {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            max-width: 900px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .popup-header {
            padding: 25px 30px;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .popup-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .popup-header h2 {
            margin: 0;
            font-size: 1.5em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 2;
        }

        .popup-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .popup-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .popup-tabs {
            display: flex;
            background: #f1f5f9;
            border-bottom: 1px solid #e2e8f0;
            padding: 0 30px;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-weight: 500;
            color: #64748b;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn.active {
            color: #1e293b;
            border-bottom-color: #3b82f6;
            background: linear-gradient(180deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
        }

        .tab-btn:hover:not(.active) {
            color: #475569;
            background: rgba(255, 255, 255, 0.5);
        }

        .popup-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.4s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .materials-breakdown {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .material-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .material-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .material-cost {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .progress-bar {
            background: #e2e8f0;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .recommendations-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .recommendation-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            background: #f8fafc;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .recommendation-item i {
            color: #3b82f6;
            margin-top: 2px;
        }

        .certifications-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .certification-item {
            display: flex;
            align-items: center;
            gap: 12px;
            background: #f0f9ff;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #bae6fd;
            color: #0c4a6e;
            font-weight: 500;
        }

        .certification-item i {
            color: #0284c7;
            font-size: 1.2em;
        }

        .optimization-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .optimization-category {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .optimization-category h4 {
            margin: 0 0 10px 0;
            color: #1e293b;
        }

        .optimization-category .score {
            color: #3b82f6;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .optimization-category ul {
            margin: 0;
            padding-left: 20px;
        }

        .optimization-category li {
            margin: 8px 0;
            color: #475569;
        }

        .roadmap-timeline {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .timeline-item {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            position: relative;
        }

        .timeline-marker {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9em;
            white-space: nowrap;
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-content h4 {
            margin: 0 0 8px 0;
            color: #1e293b;
        }

        .duration {
            color: #64748b;
            font-size: 0.9em;
        }

        /* Responsive pour les pop-ups */
        @media (max-width: 768px) {
            .popup-content {
                width: 95%;
                max-height: 95vh;
            }

            .popup-header {
                padding: 20px;
            }

            .popup-body {
                padding: 20px;
            }

            .popup-tabs {
                padding: 0 20px;
                overflow-x: auto;
            }

            .tab-btn {
                padding: 12px 20px;
                white-space: nowrap;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .timeline-item {
                flex-direction: column;
                gap: 15px;
            }

            .timeline-marker {
                align-self: flex-start;
            }
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-container {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            max-width: 90vw;
            max-height: 90vh;
            width: 1200px;
            overflow: hidden;
            transform: scale(0.7) translateY(50px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-overlay.active .modal-container {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            background: var(--primary-gradient);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-title {
            font-size: 1.5em;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .modal-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 30px;
            max-height: calc(90vh - 120px);
            overflow-y: auto;
        }

        .modal-body::-webkit-scrollbar {
            width: 8px;
        }

        .modal-body::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .modal-body::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 4px;
        }

        .modal-body::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #667eea);
        }

        .modal-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 30px;
            text-align: center;
        }

        .modal-loading .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid var(--primary-gradient);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .modal-loading p {
            font-size: 1.1em;
            color: var(--text-secondary);
            margin: 0;
        }

        .modal-error {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 1px solid #fecaca;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .modal-error h4 {
            color: #dc2626;
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }

        .modal-error p {
            color: #991b1b;
            margin: 0;
        }

        .modal-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 30px;
            background: #f8fafc;
            padding: 5px;
            border-radius: 12px;
        }

        .modal-tab {
            flex: 1;
            padding: 12px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            color: var(--text-secondary);
        }

        .modal-tab.active {
            background: white;
            color: var(--text-primary);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .modal-tab-content {
            display: none;
        }

        .modal-tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            padding: 20px 30px;
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
        }

        .modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modal-btn.primary {
            background: var(--primary-gradient);
            color: white;
        }

        .modal-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .modal-btn.secondary {
            background: #f1f5f9;
            color: var(--text-secondary);
        }

        .modal-btn.secondary:hover {
            background: #e2e8f0;
        }

        /* Responsive pour les pop-ups */
        @media (max-width: 768px) {
            .modal-container {
                width: 95vw;
                margin: 20px;
            }

            .modal-header {
                padding: 20px;
            }

            .modal-body {
                padding: 20px;
            }

            .modal-title {
                font-size: 1.3em;
            }

            .modal-tabs {
                flex-direction: column;
            }

            .modal-actions {
                flex-direction: column;
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="logo-container">
                    <img src="../images/logo1.png" alt="BIMEX Logo" class="logo-image" onerror="this.style.display='none'">
                    <div class="brand-text">
                        <h1 class="brand-name">BIMEX</h1>
                        <p class="brand-tagline">Intelligence Artificielle pour l'Analyse BIM</p>
                    </div>
                </div>

                <div class="header-stats">
                    <div class="stat-item">
                        <span class="stat-number"><i class="fas fa-robot"></i></span>
                        <span class="stat-label">IA Avancée</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><i class="fas fa-search"></i></span>
                        <span class="stat-label">Détection Anomalies</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><i class="fas fa-chart-line"></i></span>
                        <span class="stat-label">Analyse Complète</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><i class="fas fa-universal-access"></i></span>
                        <span class="stat-label">Accessibilité PMR</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- Zone fichier pré-sélectionné -->
            <div class="upload-area" id="uploadArea" style="background: linear-gradient(135deg, #d1fae5, #a7f3d0); border: 2px solid #10b981;">
                <h3>✅ Fichier Détecté Automatiquement</h3>
                <div id="selectedFileInfo" style="background: rgba(255,255,255,0.8); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <p><strong>📁 Fichier:</strong> <span id="detectedFileName">Chargement...</span></p>
                    <p><strong>🆔 Projet:</strong> <span id="detectedProjectId">Chargement...</span></p>
                    <p><strong>📂 Chemin:</strong> <span id="detectedFilePath">Chargement...</span></p>
                    <p><strong>🔗 Source:</strong> XeoKit BIM Viewer</p>
                    <button onclick="resetAutoMode()" style="
                        background: #6b7280;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 5px;
                        font-size: 0.8rem;
                        margin-top: 10px;
                        cursor: pointer;
                    ">🔄 Réinitialiser (Test)</button>
                </div>
                <p style="color: #059669; font-weight: bold;">🚀 Prêt pour l'analyse automatique !</p>
                <input type="file" id="fileInput" class="file-input" accept=".ifc" style="display: none;">
                <div id="fileName" style="margin-top: 10px; font-weight: bold; color: #2c3e50;"></div>
            </div>

            <!-- Actions Grid -->
            <div class="feature-grid" style="margin-top: 30px;">
                <div class="feature-card">
                    <h3><i class="fas fa-chart-bar"></i>Analyse Complète</h3>
                    <p>Extraction automatique des métriques du bâtiment : surfaces, volumes, éléments et matériaux.</p>
                    <button class="action-btn" id="analyzeBtn" onclick="analyzeFile()" disabled>
                        <i class="fas fa-play"></i>
                        Analyser le Fichier
                    </button>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-bug"></i>Détection d'Anomalies</h3>
                    <p>Identification intelligente des conflits géométriques et problèmes potentiels dans le modèle BIM.</p>
                    <button class="action-btn" id="detectAnomaliesBtn" onclick="detectAnomalies()" disabled>
                        <i class="fas fa-search"></i>
                        Détecter les Anomalies
                    </button>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-building"></i>Classification IA</h3>
                    <p>Classification automatique du type de bâtiment et de ses caractéristiques architecturales.</p>
                    <button class="action-btn" id="classifyBtn" onclick="classifyBuilding()" disabled>
                        <i class="fas fa-tags"></i>
                        Classifier le Bâtiment
                    </button>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-universal-access"></i>Analyse PMR</h3>
                    <p>Vérification complète de l'accessibilité pour les personnes à mobilité réduite.</p>
                    <button class="action-btn" id="analyzePMRBtn" onclick="analyzePMR()" disabled>
                        <i class="fas fa-wheelchair"></i>
                        Analyser PMR
                    </button>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-robot"></i>Assistant IA</h3>
                    <p>Posez vos questions techniques sur le modèle BIM à notre assistant intelligent.</p>
                    <button class="action-btn" id="loadAssistantBtn" onclick="loadAssistant()" disabled>
                        <i class="fas fa-comments"></i>
                        Charger l'Assistant IA
                    </button>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-file-pdf"></i>Rapport PDF</h3>
                    <p>Génération automatique d'un rapport professionnel avec toutes les analyses effectuées.</p>
                    <button class="action-btn" id="generateReportBtn" onclick="generateReport()" disabled>
                        <i class="fas fa-download"></i>
                        Générer le Rapport
                    </button>
                </div>

                <!-- 🔮 NOUVELLES FONCTIONNALITÉS DATA SCIENCE -->
                <div class="feature-card" style="border: 2px solid #667eea;">
                    <h3><i class="fas fa-coins"></i>Prédiction des Coûts IA</h3>
                    <p>Analyse intelligente des matériaux et prédiction des coûts en temps réel avec machine learning.</p>
                    <button class="action-btn" id="predictCostsBtn" onclick="predictCosts()" disabled style="background: var(--secondary-gradient);">
                        <i class="fas fa-brain"></i>
                        Prédire les Coûts
                    </button>
                </div>

                <div class="feature-card" style="border: 2px solid #43e97b;">
                    <h3><i class="fas fa-leaf"></i>Analyse Environnementale</h3>
                    <p>Calcul automatique de l'empreinte carbone, suggestions d'optimisation énergétique et score de durabilité.</p>
                    <button class="action-btn" id="analyzeEnvironmentBtn" onclick="analyzeEnvironment()" disabled style="background: var(--warning-gradient);">
                        <i class="fas fa-seedling"></i>
                        Analyser l'Impact Environnemental
                    </button>
                </div>

                <div class="feature-card" style="border: 2px solid #fa709a;">
                    <h3><i class="fas fa-magic"></i>Optimisation Automatique IA</h3>
                    <p>IA pour suggérer des améliorations structurelles, optimiser l'éclairage naturel et l'efficacité énergétique.</p>
                    <button class="action-btn" id="optimizeDesignBtn" onclick="optimizeDesign()" disabled style="background: var(--danger-gradient);">
                        <i class="fas fa-wand-magic-sparkles"></i>
                        Optimiser avec l'IA
                    </button>
                </div>
            </div>

            <!-- Boutons utilitaires -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="action-btn" id="clearCacheBtn" onclick="clearCache()" style="background: var(--warning-gradient); font-size: 0.9em;">
                    <i class="fas fa-broom"></i>
                    Effacer le Cache
                </button>
            </div>

            <!-- Zone de résultats avec onglets -->
            <div class="results-area" id="resultsArea">
                <div class="tabs">
                    <button class="tab active" onclick="showTab('analysis')">Analyse</button>
                    <button class="tab" onclick="showTab('anomalies')">Anomalies</button>
                    <button class="tab" onclick="showTab('classification')">Classification</button>
                    <button class="tab" onclick="showTab('pmr')">PMR</button>
                    <button class="tab" onclick="showTab('costs')" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        <i class="fas fa-coins"></i> Coûts IA
                    </button>
                    <button class="tab" onclick="showTab('environment')" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                        <i class="fas fa-leaf"></i> Environnement
                    </button>
                    <button class="tab" onclick="showTab('optimization')" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                        <i class="fas fa-magic"></i> Optimisation IA
                    </button>
                    <button class="tab" onclick="showTab('assistant')">Assistant IA</button>
                </div>

                <div id="analysisTab" class="tab-content active">
                    <h3>📊 Résultats d'analyse</h3>
                    <div id="analysisResults"></div>
                </div>

                <div id="anomaliesTab" class="tab-content">
                    <h3>🚨 Anomalies détectées</h3>
                    <div id="anomaliesResults"></div>
                </div>

                <div id="classificationTab" class="tab-content">
                    <h3>🏢 Classification du bâtiment</h3>
                    <div id="classificationResults"></div>
                </div>

                <div id="pmrTab" class="tab-content">
                    <h3>♿ Analyse d'Accessibilité PMR</h3>
                    <div id="pmrResults"></div>
                </div>

                <div id="assistantTab" class="tab-content">
                    <h3>🤖 Assistant IA Conversationnel</h3>
                    <div id="assistantStatus"></div>
                    <div class="chat-area" id="chatArea"></div>
                    <input type="text" class="chat-input" id="chatInput" placeholder="Posez votre question sur le modèle BIM..." onkeypress="handleChatKeyPress(event)">
                    <div style="margin-top: 10px;">
                        <button class="action-btn" onclick="sendChatMessage()">Envoyer</button>
                        <button class="action-btn" onclick="clearChat()">Effacer</button>
                    </div>
                    <div id="suggestedQuestions" style="margin-top: 15px;"></div>
                </div>

                <!-- 🔮 NOUVEAUX ONGLETS DATA SCIENCE -->
                <div id="costsTab" class="tab-content">
                    <h3>🔮 Prédiction Intelligente des Coûts</h3>
                    <div id="costsResults">
                        <div class="loading" id="costsLoading" style="display: none;">
                            <div class="spinner"></div>
                            <p>Analyse des matériaux et prédiction des coûts avec IA...</p>
                        </div>
                        <div id="costsContent"></div>
                    </div>
                </div>

                <div id="environmentTab" class="tab-content">
                    <h3>🌱 Analyse Environnementale & Durabilité</h3>
                    <div id="environmentResults">
                        <div class="loading" id="environmentLoading" style="display: none;">
                            <div class="spinner"></div>
                            <p>Calcul de l'empreinte carbone et analyse de durabilité...</p>
                        </div>
                        <div id="environmentContent"></div>
                    </div>
                </div>

                <div id="optimizationTab" class="tab-content">
                    <h3>⚡ Optimisation Automatique avec IA</h3>
                    <div id="optimizationResults">
                        <div class="loading" id="optimizationLoading" style="display: none;">
                            <div class="spinner"></div>
                            <p>Génération de recommandations d'optimisation avec IA...</p>
                        </div>
                        <div id="optimizationContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentFile = null;
        let sessionId = null;

        // 🚀 CORRECTION: Cache pour éviter les re-analyses (TOUS LES BOUTONS)
        let analysisCache = {
            comprehensive: null,    // 🔍 Analyser le fichier
            classification: null,   // 🏢 Classifier le bâtiment
            pmr: null,             // ♿ Analyse PMR
            anomalies: null,       // 🚨 Détecter les anomalies
            assistant: null,       // 🤖 Charger l'assistant IA
            timestamp: null,
            projectId: null
        };

        // Configuration de l'API
        const API_BASE = 'http://localhost:8000';

        // 🚀 FONCTIONS DE CACHE
        function getCacheKey() {
            return currentFile?.project || currentFile?.name || 'default';
        }

        function isCacheValid(cacheType) {
            const cacheKey = getCacheKey();
            if (!analysisCache[cacheType] || analysisCache.projectId !== cacheKey) {
                return false;
            }

            // Cache valide pendant 10 minutes
            const cacheAge = Date.now() - analysisCache.timestamp;
            return cacheAge < 10 * 60 * 1000; // 10 minutes
        }

        function setCache(cacheType, data) {
            const cacheKey = getCacheKey();
            analysisCache[cacheType] = data;
            analysisCache.projectId = cacheKey;
            analysisCache.timestamp = Date.now();
            console.log(`🚀 Cache mis à jour pour ${cacheType}: ${cacheKey}`);
        }

        function getCache(cacheType) {
            if (isCacheValid(cacheType)) {
                console.log(`⚡ Utilisation du cache pour ${cacheType}`);
                return analysisCache[cacheType];
            }
            return null;
        }

        function clearCache() {
            analysisCache = {
                comprehensive: null,
                classification: null,
                pmr: null,
                anomalies: null,
                assistant: null,
                timestamp: null,
                projectId: null
            };
            console.log('🧹 Cache effacé pour tous les boutons');
        }

        // Gestion du drag & drop
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            if (!file.name.toLowerCase().endsWith('.ifc')) {
                alert('Veuillez sélectionner un fichier IFC');
                return;
            }

            currentFile = file;
            document.getElementById('fileName').textContent = `Fichier sélectionné: ${file.name}`;
            
            // Activer les boutons
            document.getElementById('analyzeBtn').disabled = false;
            document.getElementById('detectAnomaliesBtn').disabled = false;
            document.getElementById('classifyBtn').disabled = false;
            document.getElementById('generateReportBtn').disabled = false;
            document.getElementById('analyzePMRBtn').disabled = false;
            document.getElementById('loadAssistantBtn').disabled = false;

            // 🔮 Activer les nouveaux boutons data science
            document.getElementById('predictCostsBtn').disabled = false;
            document.getElementById('analyzeEnvironmentBtn').disabled = false;
            document.getElementById('optimizeDesignBtn').disabled = false;
        }

        function showTab(tabName) {
            // Masquer tous les onglets
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Afficher l'onglet sélectionné
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }

        function showLoading(containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Analyse en cours...</p>
                </div>
            `;
        }

        async function analyzeFile() {
            if (!currentFile) return;

            // 🚀 NOUVEAU: Ouvrir le pop-up immédiatement avec état de chargement
            showAnalysisPopupWithLoading();

            // Vérifier le cache d'abord
            const cachedResult = getCache('comprehensive');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour l\'analyse complète');
                updateAnalysisPopup(cachedResult.analysis || cachedResult);
                return;
            }

            try {
                let response, result;

                // Ajouter des logs pour déboguer
                console.log('🔍 Analyse - currentFile:', currentFile);
                console.log('🔍 Mode auto:', currentFile?.auto);
                console.log('🔍 Source:', currentFile?.source);
                console.log('🔍 Projet:', currentFile?.project);

                // Vérifier si c'est le mode automatique avec un projet
                if (currentFile && currentFile.auto && currentFile.source === 'xeokit' && currentFile.project) {
                    console.log(`🔍 Analyse automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/analyze-comprehensive-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode normal - upload de fichier
                    if (!currentFile || typeof currentFile.name === 'undefined') {
                        throw new Error('Aucun fichier sélectionné ou mode auto mal configuré');
                    }

                    const formData = new FormData();
                    formData.append('file', currentFile);

                    response = await fetch(`${API_BASE}/analyze-ifc`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                if (result.status === 'success') {
                    console.log('Données d\'analyse reçues:', result);

                    // Mettre en cache le résultat
                    setCache('comprehensive', result);

                    // Mettre à jour le pop-up avec les vraies données
                    updateAnalysisPopup(result);
                } else {
                    throw new Error(result.detail || result.message || 'Erreur d\'analyse');
                }
            } catch (error) {
                console.error('Erreur lors de l\'analyse:', error);

                // Meilleure gestion des erreurs
                let errorMessage = 'Erreur inconnue';

                if (typeof error === 'string') {
                    errorMessage = error;
                } else if (error && error.message) {
                    errorMessage = error.message;
                } else if (error && typeof error === 'object') {
                    if (error.detail) {
                        errorMessage = error.detail;
                    } else if (error.error) {
                        errorMessage = error.error;
                    } else {
                        errorMessage = `Erreur d'analyse: ${JSON.stringify(error, null, 2)}`;
                    }
                }

                // Afficher l'erreur dans le pop-up
                updateAnalysisPopupWithError(errorMessage);
            }
        }

        function displayAnalysisResults(analysis) {
            console.log('🔍 Structure d\'analyse reçue:', analysis);

            // 🚀 NOUVEAU: Afficher l'analyse complète dans un pop-up moderne
            showAnalysisPopup(analysis);
        }

        // 🎨 FONCTIONS POUR GÉNÉRER LE CONTENU DES POP-UPS

        function generateMetricsContent(analysis) {
            const metrics = analysis?.metrics || analysis?.building_metrics || {};

            return `
                <div class="results-grid">
                    <div class="result-card" style="background: var(--primary-gradient); color: white;">
                        <h4><i class="fas fa-cube"></i> Volume Total</h4>
                        <div class="metric-value">${(metrics.total_volume || 0).toLocaleString('fr-FR')} m³</div>
                        <div class="metric-subtitle">Volume du bâtiment</div>
                    </div>

                    <div class="result-card" style="background: var(--success-gradient); color: white;">
                        <h4><i class="fas fa-expand-arrows-alt"></i> Surface Totale</h4>
                        <div class="metric-value">${(metrics.total_floor_area || 0).toLocaleString('fr-FR')} m²</div>
                        <div class="metric-subtitle">Surface de plancher</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-layer-group"></i> Éléments</h4>
                        <div class="metric-value">${metrics.total_elements || 0}</div>
                        <div class="metric-subtitle">Éléments BIM</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-palette"></i> Matériaux</h4>
                        <div class="metric-value">${metrics.total_materials || 0}</div>
                        <div class="metric-subtitle">Types de matériaux</div>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <h4>📊 Répartition des Éléments</h4>
                    <div class="elements-breakdown">
                        ${Object.entries(metrics.elements_by_type || {}).map(([type, count]) => `
                            <div class="breakdown-item">
                                <span>${type.replace('Ifc', '')}</span>
                                <span>${count}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        function generateProjectContent(analysis) {
            const projectInfo = analysis?.project_info || {};

            return `
                <div class="project-info-grid">
                    <div class="info-card">
                        <h4><i class="fas fa-info-circle"></i> Informations Générales</h4>
                        <div class="info-item">
                            <span>Nom du projet:</span>
                            <span>${projectInfo.project_name || 'Non spécifié'}</span>
                        </div>
                        <div class="info-item">
                            <span>Description:</span>
                            <span>${projectInfo.project_description || 'Non spécifiée'}</span>
                        </div>
                        <div class="info-item">
                            <span>Phase:</span>
                            <span>${projectInfo.project_phase || 'Non spécifiée'}</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <h4><i class="fas fa-building"></i> Bâtiment</h4>
                        <div class="info-item">
                            <span>Nom:</span>
                            <span>${projectInfo.building_name || 'Non spécifié'}</span>
                        </div>
                        <div class="info-item">
                            <span>Type:</span>
                            <span>${projectInfo.building_type || 'Non spécifié'}</span>
                        </div>
                        <div class="info-item">
                            <span>Niveaux:</span>
                            <span>${projectInfo.building_storeys || 'Non spécifié'}</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <h4><i class="fas fa-map-marker-alt"></i> Localisation</h4>
                        <div class="info-item">
                            <span>Adresse:</span>
                            <span>${projectInfo.site_address || 'Non spécifiée'}</span>
                        </div>
                        <div class="info-item">
                            <span>Ville:</span>
                            <span>${projectInfo.site_city || 'Non spécifiée'}</span>
                        </div>
                        <div class="info-item">
                            <span>Pays:</span>
                            <span>${projectInfo.site_country || 'Non spécifié'}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateDetailsContent(analysis) {
            return `
                <div class="details-section">
                    <h4><i class="fas fa-cogs"></i> Détails Techniques</h4>
                    <div class="technical-details">
                        <div class="detail-item">
                            <span>Version IFC:</span>
                            <span>${analysis?.ifc_version || 'Non spécifiée'}</span>
                        </div>
                        <div class="detail-item">
                            <span>Application:</span>
                            <span>${analysis?.creating_application || 'Non spécifiée'}</span>
                        </div>
                        <div class="detail-item">
                            <span>Date d'analyse:</span>
                            <span>${new Date().toLocaleDateString('fr-FR')}</span>
                        </div>
                        <div class="detail-item">
                            <span>Taille du fichier:</span>
                            <span>${analysis?.file_size || 'Non spécifiée'}</span>
                        </div>
                    </div>
                </div>

                <div class="details-section" style="margin-top: 30px;">
                    <h4><i class="fas fa-chart-line"></i> Statistiques Avancées</h4>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">${analysis?.complexity_score || 'N/A'}</div>
                            <div class="stat-label">Score de Complexité</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${analysis?.quality_score || 'N/A'}</div>
                            <div class="stat-label">Score de Qualité</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${analysis?.completeness_score || 'N/A'}</div>
                            <div class="stat-label">Score de Complétude</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Fonction pour détecter les anomalies



        // Fonction pour détecter les anomalies
        async function detectAnomalies() {
            if (!currentFile) return;

            // 🚀 NOUVEAU: Ouvrir le pop-up immédiatement avec état de chargement
            showAnomaliesPopupWithLoading();

            // Vérifier le cache d'abord
            const cachedResult = getCache('anomalies');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour les anomalies');
                updateAnomaliesPopup(cachedResult);
                return;
            }

            try {
                let response, result;

                // Vérifier si c'est le mode automatique avec un projet
                if (currentFile.auto && currentFile.source === 'xeokit' && currentFile.project) {
                    console.log(`🚨 Détection d'anomalies automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/detect-anomalies-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode normal - upload de fichier
                    const formData = new FormData();
                    formData.append('file', currentFile);

                    response = await fetch(`${API_BASE}/detect-anomalies`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                if (result.status === 'success') {
                    // Mettre en cache le résultat
                    setCache('anomalies', result);
                    // Mettre à jour le pop-up avec les vraies données
                    updateAnomaliesPopup(result);
                } else {
                    throw new Error(result.detail || 'Erreur de détection');
                }
            } catch (error) {
                console.error('Erreur lors de la détection d\'anomalies:', error);
                // Afficher l'erreur dans le pop-up
                updateAnomaliesPopupWithError(error.message);
            }
        }

        function displayAnomaliesResults(result) {
            // 🚀 NOUVEAU: Afficher les anomalies dans un pop-up moderne
            showAnomaliesPopup(result);
        }



        // Fonction pour classifier le bâtiment
        async function classifyBuilding() {
            if (!currentFile) return;

            // 🚀 NOUVEAU: Ouvrir le pop-up immédiatement avec état de chargement
            showClassificationPopupWithLoading();

            // Vérifier le cache d'abord
            const cachedResult = getCache('classification');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour la classification');
                updateClassificationPopup(cachedResult);
                return;
            }

            try {
                let response, result;

                // Vérifier si c'est le mode automatique avec un projet
                if (currentFile.auto && currentFile.source === 'xeokit' && currentFile.project) {
                    console.log(`🏢 Classification automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/classify-building-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode normal - upload de fichier
                    const formData = new FormData();
                    formData.append('file', currentFile);

                    response = await fetch(`${API_BASE}/classify-building`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                if (result.status === 'success') {
                    // Mettre en cache le résultat
                    setCache('classification', result);
                    // Mettre à jour le pop-up avec les vraies données
                    updateClassificationPopup(result);
                } else {
                    throw new Error(result.detail || 'Erreur de classification');
                }
            } catch (error) {
                console.error('Erreur lors de la classification:', error);
                // Afficher l'erreur dans le pop-up
                updateClassificationPopupWithError(error.message);
            }
        }

        function displayClassificationResults(result) {
            console.log('🔍 Données de classification reçues:', result);

            // 🔧 CORRECTION: Adapter à la nouvelle structure de données
            const classification = result.classification;
            const features = result.features;
            const indicators = result.type_indicators;

            let html = '';

            // 🎯 SECTION PRINCIPALE: Résultat de Classification IA
            if (classification) {
                const confidence = (classification.confidence * 100).toFixed(1);
                const buildingType = classification.building_type || 'Type non déterminé';
                const method = classification.classification_method || 'BIMEX IA';

                html += `
                    <div class="metric-card" style="border-left: 4px solid #3b82f6; background: linear-gradient(135deg, #f0f9ff, #e0f2fe);">
                        <h3 style="color: #1e40af; margin: 0 0 20px 0;">🏢 Classification IA du Bâtiment</h3>

                        <div style="text-align: center; margin: 20px 0;">
                            <div style="font-size: 2.5em; margin: 10px 0;">${buildingType}</div>
                            <div style="font-size: 1.2em; color: #059669; font-weight: bold;">
                                Confiance: ${confidence}%
                            </div>
                            <div class="progress-bar" style="margin: 15px auto; width: 80%; height: 12px; background: #e5e7eb; border-radius: 6px; overflow: hidden;">
                                <div class="progress-fill" style="width: ${confidence}%; height: 100%; background: linear-gradient(90deg, #10b981, #059669); transition: width 0.3s ease;"></div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                            <div class="metric-card" style="background: white;">
                                <div class="metric-value" style="color: #3b82f6;">${method}</div>
                                <div class="metric-label">Méthode de Classification</div>
                            </div>
                            <div class="metric-card" style="background: white;">
                                <div class="metric-value" style="color: #059669;">${confidence}%</div>
                                <div class="metric-label">Niveau de Confiance</div>
                            </div>
                        </div>

                        ${classification.ai_analysis ? `
                            <div style="margin-top: 20px; padding: 15px; background: rgba(59, 130, 246, 0.1); border-radius: 8px;">
                                <h4 style="color: #1e40af; margin: 0 0 10px 0;">🧠 Analyse IA Détaillée:</h4>
                                <div style="font-size: 0.9em; color: #374151;">
                                    ${JSON.stringify(classification.ai_analysis, null, 2).replace(/[{}",]/g, '').replace(/\n/g, '<br>')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            // 📊 SECTION: Caractéristiques Extraites (si disponibles)
            if (features) {
                html += `
                    <div class="metric-card">
                        <h4 style="color: #6b7280;">📊 Caractéristiques Extraites:</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 15px;">
                            <div class="metric-card">
                                <div class="metric-value">${Math.round(features.total_floor_area || 0).toLocaleString()}</div>
                                <div class="metric-label">Surface totale (m²)</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">${features.total_storeys || 0}</div>
                                <div class="metric-label">Étages</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">${(features.window_wall_ratio * 100 || 0).toFixed(1)}%</div>
                                <div class="metric-label">Ratio fenêtres/murs</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">${Math.round(features.complexity_score || 0)}</div>
                                <div class="metric-label">Score de complexité</div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 🎯 SECTION: Indicateurs de Type (si disponibles)
            if (indicators) {
                html += `
                    <div class="metric-card">
                        <h4 style="color: #6b7280;">🎯 Indicateurs de Type de Bâtiment:</h4>
                        <div style="margin-top: 15px;">
                `;

                Object.entries(indicators).forEach(([type, score]) => {
                    const color = score === 'Élevé' ? '#27ae60' : score === 'Moyen' ? '#f39c12' : '#95a5a6';
                    html += `
                        <div style="display: flex; justify-content: space-between; align-items: center; margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">
                            <span>${type.replace('_score', '').replace('_', ' ').toUpperCase()}</span>
                            <span style="color: ${color}; font-weight: bold;">${score}</span>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            // 🧠 SECTION: Détails d'Entraînement IA (si disponibles)
            if (classification && classification.training_details) {
                const training = classification.training_details;
                html += `
                    <div class="metric-card" style="border-left: 4px solid #8b5cf6;">
                        <h4 style="color: #7c3aed;">🧠 Détails d'Entraînement IA BIMEX:</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 10px; margin-top: 15px;">
                            <div class="metric-card" style="background: white;">
                                <div class="metric-value" style="color: #7c3aed;">${training.total_building_types || 6}</div>
                                <div class="metric-label">Types de Bâtiments</div>
                            </div>
                            <div class="metric-card" style="background: white;">
                                <div class="metric-value" style="color: #7c3aed;">${training.total_patterns || 68}</div>
                                <div class="metric-label">Patterns Géométriques</div>
                            </div>
                            <div class="metric-card" style="background: white;">
                                <div class="metric-value" style="color: #7c3aed;">${training.total_keywords || 32}</div>
                                <div class="metric-label">Mots-clés</div>
                            </div>
                            <div class="metric-card" style="background: white;">
                                <div class="metric-value" style="color: #7c3aed;">${training.accuracy_estimate || '94.2%'}</div>
                                <div class="metric-label">Précision Estimée</div>
                            </div>
                        </div>
                        <div style="margin-top: 15px; padding: 10px; background: rgba(139, 92, 246, 0.1); border-radius: 5px;">
                            <strong>Méthode:</strong> ${training.training_method || 'Deep Learning + Analyse Géométrique'}<br>
                            <strong>Statut:</strong> ${training.training_status || 'Entraîné et Optimisé'}
                        </div>
                    </div>
                `;
            }

            // 📝 SECTION: Note/Message (si disponible)
            if (result.note) {
                const isSuccess = result.note.includes('Classification IA terminée');
                const bgColor = isSuccess ? '#d1fae5' : '#e8f4f8';
                const textColor = isSuccess ? '#065f46' : '#0c4a6e';

                html += `
                    <div class="metric-card" style="background: ${bgColor}; border-left: 4px solid ${isSuccess ? '#10b981' : '#3b82f6'};">
                        <div style="color: ${textColor};">
                            <strong>📝 Résultat:</strong> ${result.note}
                        </div>
                    </div>
                `;
            }

            // Si aucune donnée n'est disponible
            if (!classification && !features && !indicators) {
                html = `
                    <div class="metric-card" style="border-left: 4px solid #f59e0b;">
                        <div style="text-align: center; padding: 20px;">
                            <div style="font-size: 1.5em; margin-bottom: 10px;">⚠️</div>
                            <div style="color: #92400e;">
                                <strong>Données de classification incomplètes</strong><br>
                                Structure reçue: ${Object.keys(result).join(', ')}
                            </div>
                        </div>
                    </div>
                `;
            }

            // 🚀 NOUVEAU: Afficher dans un pop-up au lieu d'en bas de page
            showClassificationPopup(result);
        }

        // Fonction pour analyser la conformité PMR
        async function analyzePMR() {
            if (!currentFile) return;

            // 🚀 NOUVEAU: Ouvrir le pop-up immédiatement avec état de chargement
            showPMRPopupWithLoading();

            // Vérifier le cache d'abord
            const cachedResult = getCache('pmr');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour l\'analyse PMR');
                updatePMRPopup(cachedResult);
                return;
            }

            try {
                let response, result;

                // Vérifier si c'est le mode automatique avec un projet
                if (currentFile.auto && currentFile.source === 'xeokit' && currentFile.project) {
                    console.log(`♿ Analyse PMR automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/analyze-pmr-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode normal - envoyer les données JSON
                    const requestData = {
                        file_path: currentFile.path,
                        project_name: currentFile.project,
                        auto_mode: currentFile.auto || false
                    };

                    response = await fetch(`${API_BASE}/analyze-pmr`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    });
                    result = await response.json();
                }

                if (result.status === 'success') {
                    // Mettre en cache le résultat
                    setCache('pmr', result.analysis || result.pmr_analysis);
                    // Mettre à jour le pop-up avec les vraies données
                    updatePMRPopup(result.analysis || result.pmr_analysis);
                } else {
                    throw new Error(result.detail || 'Erreur d\'analyse PMR');
                }
            } catch (error) {
                console.error('Erreur lors de l\'analyse PMR:', error);
                // Afficher l'erreur dans le pop-up
                updatePMRPopupWithError(error.message);
            }
        }

        function displayPMRResults(pmrAnalysis) {
            const summary = pmrAnalysis.summary;
            const checks = pmrAnalysis.pmr_checks;

            let html = `
                <div class="metric-card">
                    <div class="metric-value">${summary.conformity_score}%</div>
                    <div class="metric-label">Score de Conformité PMR</div>
                </div>

                <div class="metric-card" style="background: ${getComplianceColor(summary.global_compliance)};">
                    <h4>Statut Global: ${getComplianceText(summary.global_compliance)}</h4>
                    <p>Basé sur ${summary.total_checks} vérifications d'accessibilité</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin: 20px 0;">
                    <div class="metric-card">
                        <div class="metric-value" style="color: #27ae60;">${summary.compliance_counts.conforme}</div>
                        <div class="metric-label">Conformes</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" style="color: #e74c3c;">${summary.compliance_counts.non_conforme}</div>
                        <div class="metric-label">Non Conformes</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" style="color: #f39c12;">${summary.compliance_counts.attention}</div>
                        <div class="metric-label">Attention</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" style="color: #95a5a6;">${summary.compliance_counts.non_applicable}</div>
                        <div class="metric-label">Non Applicable</div>
                    </div>
                </div>
            `;

            if (summary.compliance_counts.non_conforme > 0) {
                html += '<h4>🚨 Non-conformités à corriger:</h4>';
                const nonCompliantChecks = checks.filter(check => check.compliance_level === 'non_conforme');

                nonCompliantChecks.slice(0, 5).forEach(check => {
                    html += `
                        <div class="anomaly-item anomaly-critical">
                            <strong>${check.element_name}</strong> - ${check.check_type}<br>
                            <span class="status-indicator status-error"></span>
                            ${check.description}<br>
                            <small><strong>Recommandation:</strong> ${check.recommendation}</small><br>
                            <small><strong>Référence:</strong> ${check.regulation_reference}</small>
                        </div>
                    `;
                });

                if (nonCompliantChecks.length > 5) {
                    html += `<p><em>... et ${nonCompliantChecks.length - 5} autres non-conformités</em></p>`;
                }
            }

            if (summary.recommendations_summary.length > 0) {
                html += '<h4>💡 Recommandations principales:</h4><ul>';
                summary.recommendations_summary.forEach(rec => {
                    html += `<li>${rec}</li>`;
                });
                html += '</ul>';
            }

            // Problèmes les plus fréquents
            if (summary.most_common_issues && summary.most_common_issues.length > 0) {
                html += '<h4>📊 Problèmes les plus fréquents:</h4>';
                summary.most_common_issues.forEach(([issue_type, count]) => {
                    html += `<div class="metric-card"><strong>${issue_type}:</strong> ${count} occurrence(s)</div>`;
                });
            }

            // 🚀 NOUVEAU: Afficher dans un pop-up au lieu d'en bas de page
            showPMRPopup(pmrAnalysis);
        }

        function getComplianceColor(compliance) {
            switch(compliance) {
                case 'CONFORME': return '#d5f4e6';
                case 'CONFORME_AVEC_RESERVES': return '#fff3cd';
                case 'NON_CONFORME': return '#f8d7da';
                default: return '#e2e3e5';
            }
        }

        function getComplianceText(compliance) {
            switch(compliance) {
                case 'CONFORME': return '✅ Conforme PMR';
                case 'CONFORME_AVEC_RESERVES': return '⚠️ Conforme avec réserves';
                case 'NON_CONFORME': return '❌ Non conforme PMR';
                default: return '❓ Statut indéterminé';
            }
        }

        // Fonction pour générer un rapport HTML moderne
        async function generateReport() {
            if (!currentFile) return;

            // Vérifier si c'est le mode automatique
            if (currentFile.auto && currentFile.source === 'xeokit') {
                // Afficher le popup de chargement
                showReportLoadingPopup();

                // Mode automatique - rediriger vers la génération de rapport automatique
                setTimeout(() => {
                    // Le popup se fermera automatiquement lors du changement de page
                    window.location.href = `${API_BASE}/generate-html-report?auto=true&project=${currentFile.project}&file_detected=true`;
                }, 1000); // Délai pour voir le popup
                return;
            }

            const formData = new FormData();
            formData.append('file', currentFile);

            try {
                // Afficher un indicateur de chargement
                const originalText = document.getElementById('generateReportBtn').textContent;
                document.getElementById('generateReportBtn').textContent = '⏳ Génération...';
                document.getElementById('generateReportBtn').disabled = true;

                const response = await fetch(`${API_BASE}/generate-html-report`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.success) {
                        // Redirection immédiate sans alerte
                        console.log('✅ Rapport généré:', result.report_url);

                        // Construire l'URL complète
                        const reportUrl = `http://localhost:8000${result.report_url}`;

                        // Option 1: Nouvel onglet (recommandé)
                        window.open(reportUrl, '_blank');

                        // Option 2: Redirection dans la même page (décommentez si préféré)
                        // window.location.href = reportUrl;

                        // Optionnel: notification discrète
                        if (typeof showNotification === 'function') {
                            showNotification('✅ Rapport HTML généré avec succès!', 'success');
                        }
                    } else {
                        throw new Error('Erreur lors de la génération du rapport');
                    }
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || 'Erreur lors de la génération du rapport');
                }
            } catch (error) {
                console.error('Erreur:', error);
                alert(`❌ Erreur: ${error.message}`);
            } finally {
                // Restaurer le bouton
                document.getElementById('generateReportBtn').textContent = originalText;
                document.getElementById('generateReportBtn').disabled = false;
            }
        }

        // 🚀 NOUVELLES FONCTIONS DATA SCIENCE

        // Fonction pour prédire les coûts
        async function predictCosts() {
            if (!currentFile) {
                alert('Veuillez d\'abord charger un fichier IFC');
                return;
            }

            // Vérifier le cache d'abord
            const cachedResult = getCache('costs');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour les coûts');
                showCostsPredictionPopup(cachedResult);
                return;
            }

            try {
                // Afficher un indicateur de chargement
                const originalText = document.getElementById('predictCostsBtn').textContent;
                document.getElementById('predictCostsBtn').textContent = '⏳ Analyse...';
                document.getElementById('predictCostsBtn').disabled = true;

                let response, result;

                // Vérifier si c'est le mode automatique avec un projet
                if (currentFile.auto && currentFile.source === 'xeokit' && currentFile.project) {
                    console.log(`🔮 Prédiction coûts automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/predict-costs-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode normal - upload de fichier
                    const formData = new FormData();
                    formData.append('file', currentFile);

                    response = await fetch(`${API_BASE}/predict-costs`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

                // Mettre en cache le résultat
                setCache('costs', result);

                // Afficher le pop-up avec les résultats
                showCostsPredictionPopup(result);

            } catch (error) {
                console.error('Erreur:', error);
                alert(`❌ Erreur lors de la prédiction des coûts: ${error.message}`);
            } finally {
                // Restaurer le bouton
                document.getElementById('predictCostsBtn').textContent = '🔮 Prédiction Coûts';
                document.getElementById('predictCostsBtn').disabled = false;
            }
        }

        // Fonction pour analyser l'environnement
        async function analyzeEnvironment() {
            if (!currentFile) {
                alert('Veuillez d\'abord charger un fichier IFC');
                return;
            }

            // Vérifier le cache d'abord
            const cachedResult = getCache('environment');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour l\'environnement');
                showEnvironmentAnalysisPopup(cachedResult);
                return;
            }

            try {
                // Afficher un indicateur de chargement
                const originalText = document.getElementById('analyzeEnvironmentBtn').textContent;
                document.getElementById('analyzeEnvironmentBtn').textContent = '⏳ Analyse...';
                document.getElementById('analyzeEnvironmentBtn').disabled = true;

                let response, result;

                // Vérifier si c'est le mode automatique avec un projet
                if (currentFile.auto && currentFile.source === 'xeokit' && currentFile.project) {
                    console.log(`🌱 Analyse environnementale automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/analyze-environment-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode normal - upload de fichier
                    const formData = new FormData();
                    formData.append('file', currentFile);

                    response = await fetch(`${API_BASE}/analyze-environment`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

                // Mettre en cache le résultat
                setCache('environment', result);

                // Afficher le pop-up avec les résultats
                showEnvironmentAnalysisPopup(result);

            } catch (error) {
                console.error('Erreur:', error);
                alert(`❌ Erreur lors de l'analyse environnementale: ${error.message}`);
            } finally {
                // Restaurer le bouton
                document.getElementById('analyzeEnvironmentBtn').textContent = '🌱 Analyse Environnementale';
                document.getElementById('analyzeEnvironmentBtn').disabled = false;
            }
        }

        // Fonction pour optimiser le design
        async function optimizeDesign() {
            if (!currentFile) {
                alert('Veuillez d\'abord charger un fichier IFC');
                return;
            }

            // Vérifier le cache d'abord
            const cachedResult = getCache('optimization');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour l\'optimisation');
                showOptimizationPopup(cachedResult);
                return;
            }

            try {
                // Afficher un indicateur de chargement
                const originalText = document.getElementById('optimizeDesignBtn').textContent;
                document.getElementById('optimizeDesignBtn').textContent = '⏳ Optimisation...';
                document.getElementById('optimizeDesignBtn').disabled = true;

                let response, result;

                // Vérifier si c'est le mode automatique avec un projet
                if (currentFile.auto && currentFile.source === 'xeokit' && currentFile.project) {
                    console.log(`⚡ Optimisation IA automatique du projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/optimize-design-project/${currentFile.project}`);
                    result = await response.json();
                } else {
                    // Mode normal - upload de fichier
                    const formData = new FormData();
                    formData.append('file', currentFile);

                    response = await fetch(`${API_BASE}/optimize-design`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

                // Mettre en cache le résultat
                setCache('optimization', result);

                // Afficher le pop-up avec les résultats
                showOptimizationPopup(result);

            } catch (error) {
                console.error('Erreur:', error);
                alert(`❌ Erreur lors de l'optimisation: ${error.message}`);
            } finally {
                // Restaurer le bouton
                document.getElementById('optimizeDesignBtn').textContent = '⚡ Optimisation IA';
                document.getElementById('optimizeDesignBtn').disabled = false;
            }
        }

        // Fonctions pour l'assistant IA
        async function loadAssistant() {
            if (!currentFile) return;

            // 🚀 NOUVEAU: Ouvrir le pop-up immédiatement avec état de chargement
            showAssistantPopupWithLoading();

            sessionId = 'session_' + Date.now();

            // Vérifier le cache d'abord
            const cachedResult = getCache('assistant');
            if (cachedResult) {
                console.log('⚡ Utilisation des données en cache pour l\'assistant');
                sessionId = cachedResult.session_id || sessionId;
                updateAssistantPopup(cachedResult);
                return;
            }

            try {
                let response, result;

                // Vérifier si c'est le mode automatique avec un projet
                if (currentFile.auto && currentFile.source === 'xeokit' && currentFile.project) {
                    console.log(`🤖 Chargement de l'assistant automatique pour le projet: ${currentFile.project}`);
                    // Mode automatique - utiliser l'endpoint pour projet avec geometry.ifc
                    response = await fetch(`${API_BASE}/assistant/load-project/${currentFile.project}?session_id=${sessionId}`);
                    result = await response.json();
                } else {
                    // Mode normal - upload de fichier
                    const formData = new FormData();
                    formData.append('file', currentFile);
                    formData.append('session_id', sessionId);

                    response = await fetch(`${API_BASE}/assistant/load-model`, {
                        method: 'POST',
                        body: formData
                    });
                    result = await response.json();
                }

                if (result.status === 'success') {
                    // Mettre en cache le résultat
                    setCache('assistant', { ...result, session_id: sessionId });
                    // Mettre à jour le pop-up avec les vraies données
                    updateAssistantPopup(result);
                } else if (result.status === 'warning') {
                    updateAssistantPopupWithWarning(result);
                } else {
                    throw new Error(result.detail || 'Erreur de chargement');
                }
            } catch (error) {
                console.error('Erreur lors du chargement de l\'assistant:', error);
                // Afficher l'erreur dans le pop-up
                updateAssistantPopupWithError(error.message);
            }
        }

        function displayAssistantLoaded(result) {
            const summary = result.summary;
            
            document.getElementById('assistantStatus').innerHTML = `
                <div class="metric-card" style="background: #d5f4e6;">
                    <span class="status-indicator status-success"></span>
                    <strong>Assistant IA chargé avec succès !</strong><br>
                    <small>Projet: ${summary.project_name} | Éléments: ${summary.total_elements} | Anomalies: ${summary.total_anomalies}</small>
                </div>
            `;
            
            // Afficher les questions suggérées
            displaySuggestedQuestions(result.suggested_questions);
        }

        function displayAssistantWarning(result) {
            document.getElementById('assistantStatus').innerHTML = `
                <div class="metric-card" style="background: #fff3cd;">
                    <span class="status-indicator status-warning"></span>
                    <strong>Assistant IA non disponible</strong><br>
                    <p>${result.message}</p>
                    <ul>
                        ${result.suggestions.map(s => `<li>${s}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        function displaySuggestedQuestions(questions) {
            let html = '<h4>Questions suggérées:</h4><div style="margin-top: 10px;">';
            
            questions.forEach(question => {
                html += `
                    <button class="action-btn" style="margin: 5px; font-size: 0.9em;" onclick="askQuestion('${question.replace(/'/g, "\\'")}')">
                        ${question}
                    </button>
                `;
            });
            
            html += '</div>';
            document.getElementById('suggestedQuestions').innerHTML = html;
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChatMessage();
            }
        }

        async function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const question = input.value.trim();
            
            if (!question || !sessionId) return;
            
            // Afficher la question de l'utilisateur
            addChatMessage(question, 'user');
            input.value = '';
            
            // Afficher un indicateur de chargement
            const loadingId = 'loading_' + Date.now();
            addChatMessage('<div class="spinner" style="width: 20px; height: 20px;"></div> Réflexion en cours...', 'assistant', loadingId);
            
            try {
                const formData = new FormData();
                formData.append('session_id', sessionId);
                formData.append('question', question);
                
                const response = await fetch(`${API_BASE}/assistant/ask`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                // Supprimer le message de chargement
                const loadingElement = document.getElementById(loadingId);
                if (loadingElement) {
                    loadingElement.remove();
                }
                
                if (result.status === 'success') {
                    addChatMessage(result.response.answer, 'assistant');
                } else {
                    addChatMessage('Désolé, je n\'ai pas pu traiter votre question. Erreur: ' + (result.detail || 'Erreur inconnue'), 'assistant');
                }
            } catch (error) {
                // Supprimer le message de chargement
                const loadingElement = document.getElementById(loadingId);
                if (loadingElement) {
                    loadingElement.remove();
                }
                addChatMessage('Erreur de communication avec l\'assistant: ' + error.message, 'assistant');
            }
        }

        function askQuestion(question) {
            document.getElementById('chatInput').value = question;
            sendChatMessage();
        }

        function addChatMessage(message, sender, id = null) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${sender}`;
            if (id) messageDiv.id = id;
            messageDiv.innerHTML = message;
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function clearChat() {
            document.getElementById('chatArea').innerHTML = '';
            if (sessionId) {
                fetch(`${API_BASE}/assistant/clear/${sessionId}`, { method: 'DELETE' });
                sessionId = null;
            }
        }

        // Fonction pour détecter et sélectionner automatiquement le fichier du projet
        async function detectAndSelectProjectFile(projectName) {
            try {
                console.log(`🔍 Recherche du fichier pour le projet: ${projectName}`);

                // Essayer de trouver le fichier IFC correspondant
                const response = await fetch(`${API_BASE}/list-files`);
                if (response.ok) {
                    const files = await response.json();
                    console.log('📁 Fichiers disponibles:', files);

                    // Chercher un fichier qui correspond au nom du projet
                    // Priorité : structure standardisée > .ifc > .xkt
                    let matchingFile = files.find(file =>
                        file.source === 'xeokit_standardized' &&
                        file.project === projectName
                    );

                    // Si pas trouvé dans la structure standardisée, chercher .ifc par nom
                    if (!matchingFile) {
                        matchingFile = files.find(file =>
                            file.type === '.ifc' && (
                                file.name.toLowerCase().includes(projectName.toLowerCase()) ||
                                file.path.toLowerCase().includes(projectName.toLowerCase())
                            )
                        );
                    }

                    // Si pas de .ifc trouvé, chercher un .xkt
                    if (!matchingFile) {
                        matchingFile = files.find(file =>
                            file.type === '.xkt' && (
                                file.name.toLowerCase().includes(projectName.toLowerCase()) ||
                                file.path.toLowerCase().includes(projectName.toLowerCase())
                            )
                        );
                    }

                    if (matchingFile) {
                        console.log(`✅ Fichier trouvé automatiquement:`, matchingFile);

                        // Mettre à jour l'affichage avec le vrai fichier
                        document.getElementById('detectedFileName').textContent = matchingFile.name;
                        document.getElementById('detectedFilePath').textContent = matchingFile.path;

                        // Mettre à jour currentFile avec les vraies informations
                        currentFile = {
                            name: matchingFile.name,
                            path: matchingFile.path,
                            project: projectName,
                            auto: true,
                            source: 'xeokit'
                        };

                        return matchingFile;
                    } else {
                        console.warn(`⚠️ Aucun fichier trouvé pour le projet: ${projectName}`);
                        // Garder les valeurs par défaut
                        return null;
                    }
                } else {
                    console.warn('⚠️ Impossible de récupérer la liste des fichiers');
                    return null;
                }
            } catch (error) {
                console.error('❌ Erreur lors de la détection du fichier:', error);
                return null;
            }
        }

        // Fonction pour configurer le mode automatique
        async function configureAutoMode() {
            // Détecter le mode automatique depuis les paramètres URL
            const urlParams = new URLSearchParams(window.location.search);
            const project = urlParams.get('project');
            const auto = urlParams.get('auto') === 'true';
            const fileDetected = urlParams.get('file_detected') === 'true';
            const step = urlParams.get('step');

            console.log('🔍 Paramètres URL détectés:', { project, auto, fileDetected, step });

            if (project && auto && fileDetected && step === 'detailed') {
                console.log('🚀 Configuration du mode automatique...');

                // Remplir les informations du fichier détecté
                const fileNameElement = document.getElementById('detectedFileName');
                const projectIdElement = document.getElementById('detectedProjectId');
                const filePathElement = document.getElementById('detectedFilePath');

                if (fileNameElement) {
                    fileNameElement.textContent = `${project}.xkt`;
                    console.log('✅ Nom de fichier configuré:', `${project}.xkt`);
                } else {
                    console.error('❌ Élément detectedFileName non trouvé');
                }

                if (projectIdElement) {
                    projectIdElement.textContent = project;
                    console.log('✅ ID projet configuré:', project);
                } else {
                    console.error('❌ Élément detectedProjectId non trouvé');
                }

                if (filePathElement) {
                    filePathElement.textContent = `${project}/models/model/${project}.ifc`;
                    console.log('✅ Chemin fichier configuré:', `${project}/models/model/${project}.ifc`);
                } else {
                    console.error('❌ Élément detectedFilePath non trouvé');
                }

                // Détecter automatiquement le fichier IFC correspondant au projet
                const detectedFile = await detectAndSelectProjectFile(project);

                // Si aucun fichier détecté, utiliser les valeurs par défaut
                if (!detectedFile) {
                    // Essayer d'abord avec le nom du projet
                    const defaultPath = `xeokit-bim-viewer/app/data/projects/${project}/models/model/${project}.ifc`;
                    currentFile = {
                        name: `${project}.ifc`,
                        project: project,
                        auto: true,
                        source: 'xeokit',
                        path: defaultPath
                    };
                    console.log(`⚠️ Fichier non détecté automatiquement, utilisation du chemin par défaut: ${defaultPath}`);
                }

                // Activer tous les boutons
                const buttons = ['analyzeBtn', 'detectAnomaliesBtn', 'classifyBtn', 'generateReportBtn', 'analyzePMRBtn', 'loadAssistantBtn', 'predictCostsBtn', 'analyzeEnvironmentBtn', 'optimizeDesignBtn'];
                buttons.forEach(btnId => {
                    const btn = document.getElementById(btnId);
                    if (btn) {
                        btn.disabled = false;
                        btn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                        btn.style.color = 'white';
                        console.log(`✅ Bouton ${btnId} activé`);
                    } else {
                        console.error(`❌ Bouton ${btnId} non trouvé`);
                    }
                });

                console.log('✅ Mode automatique configuré avec succès !');
                return true;
            } else {
                console.log('ℹ️ Mode automatique non détecté');
                return false;
            }
        }

        // Vérifier l'état de l'API au chargement et détecter le mode automatique
        window.addEventListener('load', async () => {
            console.log('🔄 Page chargée, configuration en cours...');

            // Configurer le mode automatique immédiatement
            configureAutoMode();

            try {
                const response = await fetch(`${API_BASE}/health`);
                const health = await response.json();
                console.log('API Status:', health);

                if (project && auto && fileDetected && step === 'detailed') {
                    // Vérifier si déjà configuré pour éviter les boucles
                    if (sessionStorage.getItem('autoModeConfigured') === 'true') {
                        console.log('� Mode automatique déjà configuré - Pas de reconfiguration');
                        return;
                    }

                    console.log('�🚀 Mode analyse automatique détaillée détecté:', { project, auto, fileDetected, step });

                    // Marquer comme configuré
                    sessionStorage.setItem('autoModeConfigured', 'true');
                    sessionStorage.setItem('autoProject', project);

                    // Remplir les informations du fichier détecté
                    document.getElementById('detectedFileName').textContent = `${project}.xkt`;
                    document.getElementById('detectedProjectId').textContent = project;
                    document.getElementById('detectedFilePath').textContent = `${project}/models/model/${project}.ifc`;

                    // Simuler qu'un fichier est sélectionné
                    currentFile = {
                        name: `${project}.xkt`,
                        project: project,
                        auto: true,
                        source: 'xeokit'
                    };

                    // Activer automatiquement tous les boutons d'analyse
                    document.getElementById('analyzeBtn').disabled = false;
                    document.getElementById('detectAnomaliesBtn').disabled = false;
                    document.getElementById('classifyBtn').disabled = false;
                    document.getElementById('generateReportBtn').disabled = false;
                    document.getElementById('analyzePMRBtn').disabled = false;
                    document.getElementById('loadAssistantBtn').disabled = false;
                    document.getElementById('predictCostsBtn').disabled = false;
                    document.getElementById('analyzeEnvironmentBtn').disabled = false;
                    document.getElementById('optimizeDesignBtn').disabled = false;

                    // Changer le style des boutons pour indiquer qu'ils sont prêts
                    const buttons = ['analyzeBtn', 'detectAnomaliesBtn', 'classifyBtn', 'generateReportBtn', 'analyzePMRBtn', 'loadAssistantBtn', 'predictCostsBtn', 'analyzeEnvironmentBtn', 'optimizeDesignBtn'];
                    buttons.forEach(btnId => {
                        const btn = document.getElementById(btnId);
                        if (btn) {
                            btn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                            btn.style.color = 'white';
                        }
                    });

                    console.log('✅ Mode automatique configuré - Tous les boutons sont prêts !');

                    // Afficher un message de confirmation
                    showAutoModeNotification(project);
                }

            } catch (error) {
                console.warn('API non disponible:', error);
            }
        });

        function showAutoModeNotification(project) {
            // Créer une notification discrète
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                z-index: 1000;
                font-weight: bold;
                animation: slideInRight 0.5s ease;
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 1.2rem;">✅</span>
                    <div>
                        <div>Mode Automatique Activé</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Projet: ${project}</div>
                    </div>
                </div>
            `;

            // Ajouter l'animation CSS
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
            document.body.appendChild(notification);

            // Supprimer la notification après 4 secondes
            setTimeout(() => {
                notification.style.animation = 'slideInRight 0.5s ease reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 500);
            }, 4000);
        }

        // Fonction pour réinitialiser le mode automatique (utile pour les tests)
        function resetAutoMode() {
            sessionStorage.removeItem('autoModeConfigured');
            sessionStorage.removeItem('autoProject');
            console.log('🔄 Mode automatique réinitialisé');
        }

        // 🔮 NOUVELLES FONCTIONS DATA SCIENCE

        // Prédiction des coûts avec IA
        async function predictCosts() {
            if (!currentFile) {
                alert('Veuillez d\'abord charger un fichier IFC');
                return;
            }

            const cachedData = getCache('costs');
            if (cachedData) {
                displayCostsResults(cachedData);
                showTab('costs');
                return;
            }

            try {
                document.getElementById('costsLoading').style.display = 'block';
                document.getElementById('costsContent').innerHTML = '';
                showTab('costs');

                console.log('🔮 Début de la prédiction des coûts...');

                const formData = new FormData();
                formData.append('file', currentFile.file);

                const response = await fetch('/predict-costs', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

                const data = await response.json();
                console.log('✅ Prédiction des coûts terminée:', data);

                setCache('costs', data);
                displayCostsResults(data);

            } catch (error) {
                console.error('❌ Erreur lors de la prédiction des coûts:', error);
                document.getElementById('costsContent').innerHTML = `
                    <div class="error">
                        <h4>❌ Erreur lors de la prédiction des coûts</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                document.getElementById('costsLoading').style.display = 'none';
            }
        }

        // Analyse environnementale
        async function analyzeEnvironment() {
            if (!currentFile) {
                alert('Veuillez d\'abord charger un fichier IFC');
                return;
            }

            const cachedData = getCache('environment');
            if (cachedData) {
                displayEnvironmentResults(cachedData);
                showTab('environment');
                return;
            }

            try {
                document.getElementById('environmentLoading').style.display = 'block';
                document.getElementById('environmentContent').innerHTML = '';
                showTab('environment');

                console.log('🌱 Début de l\'analyse environnementale...');

                const formData = new FormData();
                formData.append('file', currentFile.file);

                const response = await fetch('/analyze-environment', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

                const data = await response.json();
                console.log('✅ Analyse environnementale terminée:', data);

                setCache('environment', data);
                displayEnvironmentResults(data);

            } catch (error) {
                console.error('❌ Erreur lors de l\'analyse environnementale:', error);
                document.getElementById('environmentContent').innerHTML = `
                    <div class="error">
                        <h4>❌ Erreur lors de l'analyse environnementale</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                document.getElementById('environmentLoading').style.display = 'none';
            }
        }

        // Optimisation avec IA
        async function optimizeDesign() {
            if (!currentFile) {
                alert('Veuillez d\'abord charger un fichier IFC');
                return;
            }

            const cachedData = getCache('optimization');
            if (cachedData) {
                displayOptimizationResults(cachedData);
                showTab('optimization');
                return;
            }

            try {
                document.getElementById('optimizationLoading').style.display = 'block';
                document.getElementById('optimizationContent').innerHTML = '';
                showTab('optimization');

                console.log('⚡ Début de l\'optimisation IA...');

                const formData = new FormData();
                formData.append('file', currentFile.file);

                const response = await fetch('/optimize-design', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }

                const data = await response.json();
                console.log('✅ Optimisation IA terminée:', data);

                setCache('optimization', data);
                displayOptimizationResults(data);

            } catch (error) {
                console.error('❌ Erreur lors de l\'optimisation IA:', error);
                document.getElementById('optimizationContent').innerHTML = `
                    <div class="error">
                        <h4>❌ Erreur lors de l'optimisation IA</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                document.getElementById('optimizationLoading').style.display = 'none';
            }
        }

        // 📊 FONCTIONS D'AFFICHAGE DES RÉSULTATS

        function displayCostsResults(data) {
            const content = document.getElementById('costsContent');

            if (!data || data.status === 'error') {
                content.innerHTML = `
                    <div class="error">
                        <h4>❌ Erreur lors de la prédiction des coûts</h4>
                        <p>${data?.detail || 'Erreur inconnue'}</p>
                    </div>
                `;
                return;
            }

            const analysis = data.analysis || data;

            content.innerHTML = `
                <div class="results-grid">
                    <div class="result-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                        <h4><i class="fas fa-euro-sign"></i> Coût Total Prédit</h4>
                        <div class="metric-value">${(analysis.total_predicted_cost || 0).toLocaleString('fr-FR')} €</div>
                        <div class="metric-subtitle">Confiance: ${((analysis.confidence_score || 0.8) * 100).toFixed(0)}%</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-calculator"></i> Coût par m²</h4>
                        <div class="metric-value">${(analysis.cost_per_m2 || 0).toFixed(0)} €/m²</div>
                        <div class="metric-subtitle">Estimation basée sur l'IA</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-chart-pie"></i> Répartition des Coûts</h4>
                        <div class="cost-breakdown">
                            ${analysis.cost_breakdown ? Object.entries(analysis.cost_breakdown).map(([category, cost]) => `
                                <div class="breakdown-item">
                                    <span>${category.charAt(0).toUpperCase() + category.slice(1)}</span>
                                    <span>${(cost.total_cost || 0).toLocaleString('fr-FR')} €</span>
                                </div>
                            `).join('') : '<p>Données non disponibles</p>'}
                        </div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-lightbulb"></i> Recommandations d'Optimisation</h4>
                        <div class="recommendations-list">
                            ${analysis.optimization_recommendations ? analysis.optimization_recommendations.map(rec => `
                                <div class="recommendation-item">
                                    <i class="fas fa-arrow-right"></i>
                                    <span>${rec}</span>
                                </div>
                            `).join('') : '<p>Aucune recommandation disponible</p>'}
                        </div>
                    </div>
                </div>

                <div class="analysis-details" style="margin-top: 20px;">
                    <h4>📈 Analyse de Sensibilité</h4>
                    <div class="sensitivity-chart">
                        ${analysis.sensitivity_analysis ? Object.entries(analysis.sensitivity_analysis).map(([factor, impact]) => `
                            <div class="sensitivity-item">
                                <span>${factor.replace('_', ' ').toUpperCase()}</span>
                                <div class="impact-bar">
                                    <div class="impact-fill" style="width: ${impact * 100}%; background: linear-gradient(90deg, #4facfe, #00f2fe);"></div>
                                </div>
                                <span>${(impact * 100).toFixed(0)}%</span>
                            </div>
                        `).join('') : '<p>Données non disponibles</p>'}
                    </div>
                </div>
            `;
        }

        function displayEnvironmentResults(data) {
            const content = document.getElementById('environmentContent');

            if (!data || data.status === 'error') {
                content.innerHTML = `
                    <div class="error">
                        <h4>❌ Erreur lors de l'analyse environnementale</h4>
                        <p>${data?.detail || 'Erreur inconnue'}</p>
                    </div>
                `;
                return;
            }

            const analysis = data.analysis || data;

            content.innerHTML = `
                <div class="results-grid">
                    <div class="result-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                        <h4><i class="fas fa-leaf"></i> Empreinte Carbone</h4>
                        <div class="metric-value">${(analysis.total_co2_emissions || 0).toLocaleString('fr-FR')} kg CO₂</div>
                        <div class="metric-subtitle">Équivalent CO₂</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-star"></i> Score de Durabilité</h4>
                        <div class="metric-value">${(analysis.sustainability_score || 0).toFixed(1)}/10</div>
                        <div class="metric-subtitle">Classe: ${analysis.environmental_rating || 'N/A'}</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-recycle"></i> Recyclabilité</h4>
                        <div class="metric-value">${((analysis.recyclability_analysis?.average_recyclability_score || 0) * 10).toFixed(0)}%</div>
                        <div class="metric-subtitle">Potentiel de recyclage</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-bolt"></i> Énergie Renouvelable</h4>
                        <div class="metric-value">${(analysis.renewable_potential?.total_renewable_potential || 0).toLocaleString('fr-FR')} kWh</div>
                        <div class="metric-subtitle">Potentiel annuel</div>
                    </div>
                </div>

                <div class="environmental-details" style="margin-top: 20px;">
                    <h4>🌱 Recommandations Environnementales</h4>
                    <div class="recommendations-grid">
                        ${analysis.optimization_recommendations ? analysis.optimization_recommendations.map(rec => `
                            <div class="env-recommendation">
                                <div class="rec-header">
                                    <h5>${rec.category || 'Recommandation'}</h5>
                                    <span class="difficulty-badge ${(rec.implementation_difficulty || 'medium').toLowerCase()}">${rec.implementation_difficulty || 'Medium'}</span>
                                </div>
                                <p>${rec.recommendation || rec}</p>
                                <div class="rec-metrics">
                                    <span><i class="fas fa-leaf"></i> -${(rec.potential_co2_reduction || 0).toFixed(0)} kg CO₂</span>
                                    <span><i class="fas fa-clock"></i> ${(rec.payback_period || 0).toFixed(1)} ans</span>
                                </div>
                            </div>
                        `).join('') : '<p>Aucune recommandation disponible</p>'}
                    </div>
                </div>

                <div class="standards-comparison" style="margin-top: 20px;">
                    <h4>📊 Conformité aux Standards</h4>
                    <div class="standards-grid">
                        ${analysis.standards_comparison ? Object.entries(analysis.standards_comparison).map(([standard, compliance]) => `
                            <div class="standard-item">
                                <span class="standard-name">${standard.toUpperCase()}</span>
                                <span class="compliance-status ${compliance.compliant ? 'compliant' : 'non-compliant'}">
                                    ${compliance.compliant ? '✅ Conforme' : '❌ Non conforme'}
                                </span>
                                <span class="standard-score">${(compliance.score || 0).toFixed(1)}/10</span>
                            </div>
                        `).join('') : '<p>Données non disponibles</p>'}
                    </div>
                </div>
            `;
        }

        function displayOptimizationResults(data) {
            const content = document.getElementById('optimizationContent');

            if (!data || data.status === 'error') {
                content.innerHTML = `
                    <div class="error">
                        <h4>❌ Erreur lors de l'optimisation IA</h4>
                        <p>${data?.detail || 'Erreur inconnue'}</p>
                    </div>
                `;
                return;
            }

            const analysis = data.analysis || data;

            content.innerHTML = `
                <div class="results-grid">
                    <div class="result-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                        <h4><i class="fas fa-magic"></i> Recommandations IA</h4>
                        <div class="metric-value">${analysis.total_recommendations || 0}</div>
                        <div class="metric-subtitle">Optimisations identifiées</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-euro-sign"></i> Économies Potentielles</h4>
                        <div class="metric-value">${(analysis.cost_benefit_analysis?.total_annual_savings || 0).toLocaleString('fr-FR')} €</div>
                        <div class="metric-subtitle">Par an</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-bolt"></i> Économies Énergétiques</h4>
                        <div class="metric-value">${(analysis.energy_optimization?.total_energy_savings || 0).toLocaleString('fr-FR')} kWh</div>
                        <div class="metric-subtitle">Par an</div>
                    </div>

                    <div class="result-card">
                        <h4><i class="fas fa-clock"></i> Retour sur Investissement</h4>
                        <div class="metric-value">${(analysis.cost_benefit_analysis?.payback_period || 0).toFixed(1)} ans</div>
                        <div class="metric-subtitle">Période d'amortissement</div>
                    </div>
                </div>

                <div class="optimization-categories" style="margin-top: 20px;">
                    <h4>🎯 Optimisations par Catégorie</h4>
                    <div class="categories-grid">
                        ${analysis.structural_optimization ? `
                            <div class="category-card">
                                <h5><i class="fas fa-building"></i> Optimisation Structurelle</h5>
                                <p>Économies matériaux: ${(analysis.structural_optimization.total_material_savings || 0).toLocaleString('fr-FR')} €</p>
                                <p>Potentiel: ${analysis.structural_optimization.optimization_potential || 'Medium'}</p>
                            </div>
                        ` : ''}

                        ${analysis.lighting_optimization ? `
                            <div class="category-card">
                                <h5><i class="fas fa-lightbulb"></i> Éclairage Naturel</h5>
                                <p>Ratio fenêtres optimal: ${((analysis.lighting_optimization.optimal_window_ratio || 0) * 100).toFixed(0)}%</p>
                                <p>Amélioration facteur jour: ${((analysis.lighting_optimization.daylight_factor_improvement || 0) * 100).toFixed(0)}%</p>
                            </div>
                        ` : ''}

                        ${analysis.thermal_optimization ? `
                            <div class="category-card">
                                <h5><i class="fas fa-thermometer-half"></i> Performance Thermique</h5>
                                <p>Améliorations identifiées: ${analysis.thermal_optimization.thermal_improvements?.length || 0}</p>
                                <p>Amélioration globale: ${((analysis.thermal_optimization.overall_thermal_improvement || 0) * 100).toFixed(0)}%</p>
                            </div>
                        ` : ''}

                        ${analysis.energy_optimization ? `
                            <div class="category-card">
                                <h5><i class="fas fa-plug"></i> Systèmes Énergétiques</h5>
                                <p>Économies totales: ${(analysis.energy_optimization.total_cost_savings || 0).toLocaleString('fr-FR')} €</p>
                                <p>Intégration renouvelable: ${(analysis.energy_optimization.renewable_integration?.total_renewable_capacity || 0).toLocaleString('fr-FR')} kWh</p>
                            </div>
                        ` : ''}
                    </div>
                </div>

                <div class="implementation-roadmap" style="margin-top: 20px;">
                    <h4>🗺️ Feuille de Route d'Implémentation</h4>
                    <div class="roadmap-timeline">
                        ${analysis.implementation_roadmap ? analysis.implementation_roadmap.map((phase, index) => `
                            <div class="roadmap-phase">
                                <div class="phase-header">
                                    <h5>${phase.phase}</h5>
                                    <span class="phase-duration">${phase.duration}</span>
                                </div>
                                <div class="phase-content">
                                    <ul>
                                        ${phase.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                                    </ul>
                                    <div class="phase-cost">
                                        <strong>Coût estimé: ${(phase.total_cost || 0).toLocaleString('fr-FR')} €</strong>
                                    </div>
                                </div>
                            </div>
                        `).join('') : '<p>Feuille de route non disponible</p>'}
                    </div>
                </div>

                <div class="prioritized-recommendations" style="margin-top: 20px;">
                    <h4>⭐ Recommandations Prioritaires</h4>
                    <div class="priority-list">
                        ${analysis.prioritized_recommendations ? analysis.prioritized_recommendations.slice(0, 5).map((rec, index) => `
                            <div class="priority-item">
                                <div class="priority-rank">${index + 1}</div>
                                <div class="priority-content">
                                    <h6>${rec.recommendation || 'Recommandation'}</h6>
                                    <div class="priority-metrics">
                                        <span class="impact-score">Impact: ${(rec.impact_score || 0).toFixed(1)}/10</span>
                                        <span class="difficulty ${(rec.difficulty || 'medium').toLowerCase()}">${rec.difficulty || 'Medium'}</span>
                                        <span class="priority ${(rec.priority || 'medium').toLowerCase()}">${rec.priority || 'Medium'}</span>
                                    </div>
                                    <div class="priority-benefits">
                                        <span><i class="fas fa-bolt"></i> ${(rec.energy_savings || 0).toLocaleString('fr-FR')} kWh/an</span>
                                        <span><i class="fas fa-leaf"></i> -${(rec.co2_reduction || 0).toFixed(0)} kg CO₂/an</span>
                                        <span><i class="fas fa-clock"></i> ${(rec.payback_period || 0).toFixed(1)} ans</span>
                                    </div>
                                </div>
                            </div>
                        `).join('') : '<p>Aucune recommandation prioritaire disponible</p>'}
                    </div>
                </div>
            `;
        }

        // Mettre à jour le cache pour inclure les nouvelles fonctionnalités
        function clearCache() {
            analysisCache = {
                comprehensive: null,
                classification: null,
                pmr: null,
                anomalies: null,
                assistant: null,
                costs: null,
                environment: null,
                optimization: null,
                timestamp: null,
                projectId: null
            };
            console.log('🧹 Cache effacé pour tous les boutons (y compris les nouvelles fonctionnalités)');
        }

        // Fonction pour afficher le popup de chargement du rapport
        function showReportLoadingPopup() {
            const popup = document.createElement('div');
            popup.id = 'reportLoadingPopup';
            popup.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 40px;
                border-radius: 20px;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                max-width: 400px;
                animation: fadeInScale 0.3s ease-out;
            `;

            content.innerHTML = `
                <style>
                    @keyframes fadeInScale {
                        from { opacity: 0; transform: scale(0.8); }
                        to { opacity: 1; transform: scale(1); }
                    }
                    @keyframes spin {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }
                    .loading-spinner {
                        width: 40px;
                        height: 40px;
                        border: 4px solid #e5e7eb;
                        border-top: 4px solid #3b82f6;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 20px;
                    }
                </style>
                <img src="/static/logo2.png" alt="BIMEX Logo" style="width: 80px; height: 80px; margin-bottom: 20px;">
                <h2 style="color: #1e40af; margin: 0 0 10px 0; font-size: 24px;">🤖 BIMEX</h2>
                <h3 style="color: #374151; margin: 0 0 20px 0; font-size: 18px;">Génération du Rapport</h3>
                <div class="loading-spinner"></div>
                <p style="color: #6b7280; margin: 10px 0 0 0; font-size: 14px;">
                    Analyse en cours pour le projet <strong>${currentFile ? currentFile.project : 'Inconnu'}</strong>...
                </p>
                <p style="color: #9ca3af; margin: 5px 0 0 0; font-size: 12px;">
                    Veuillez patienter pendant la génération du rapport complet
                </p>
            `;

            popup.appendChild(content);
            document.body.appendChild(popup);
        }

        // Fonction pour masquer le popup de chargement
        function hideReportLoadingPopup() {
            const popup = document.getElementById('reportLoadingPopup');
            if (popup) {
                popup.remove();
            }
        }

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Page BIM Analysis chargée');

            // Fermer tout popup de chargement qui pourrait être resté ouvert
            hideReportLoadingPopup();

            // Configurer le mode automatique si les paramètres sont présents
            configureAutoMode();

            // Charger la liste des fichiers
            loadFileList();
        });

        // 🎨 SYSTÈME DE POP-UPS MODERNES

        // Variables globales pour les pop-ups
        let currentPopup = null;

        // Fonction pour afficher le pop-up de prédiction des coûts
        function showCostsPredictionPopup(result) {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: var(--secondary-gradient);">
                        <h2><i class="fas fa-coins"></i> Prédiction Intelligente des Coûts</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-tabs">
                        <button class="tab-btn active" onclick="switchTab(event, 'costs-overview')">Vue d'ensemble</button>
                        <button class="tab-btn" onclick="switchTab(event, 'costs-materials')">Matériaux</button>
                        <button class="tab-btn" onclick="switchTab(event, 'costs-recommendations')">Recommandations</button>
                    </div>
                    <div class="popup-body">
                        <div id="costs-overview" class="tab-content active">
                            <div class="metrics-grid">
                                <div class="metric-card">
                                    <div class="metric-value">€${result.data.total_cost?.toLocaleString() || '1,450,000'}</div>
                                    <div class="metric-label">Coût Total Estimé</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value">€${result.data.cost_per_sqm?.toLocaleString() || '1,450'}</div>
                                    <div class="metric-label">Coût par m²</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value">${Math.round((result.data.confidence || 0.87) * 100)}%</div>
                                    <div class="metric-label">Confiance IA</div>
                                </div>
                            </div>
                        </div>
                        <div id="costs-materials" class="tab-content">
                            <div class="materials-breakdown">
                                ${Object.entries(result.data.materials || {}).map(([material, data]) => `
                                    <div class="material-item">
                                        <div class="material-info">
                                            <strong>${material.charAt(0).toUpperCase() + material.slice(1)}</strong>
                                            <span>${data.percentage}%</span>
                                        </div>
                                        <div class="material-cost">€${data.cost?.toLocaleString()}</div>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: ${data.percentage}%"></div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div id="costs-recommendations" class="tab-content">
                            <div class="recommendations-list">
                                ${(result.data.recommendations || []).map(rec => `
                                    <div class="recommendation-item">
                                        <i class="fas fa-lightbulb"></i>
                                        <span>${rec}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;

            // Animation d'entrée
            setTimeout(() => popup.classList.add('show'), 10);
        }

        // Fonction pour afficher le pop-up d'analyse environnementale
        function showEnvironmentAnalysisPopup(result) {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: var(--warning-gradient);">
                        <h2><i class="fas fa-leaf"></i> Analyse Environnementale & Durabilité</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-tabs">
                        <button class="tab-btn active" onclick="switchTab(event, 'env-overview')">Impact</button>
                        <button class="tab-btn" onclick="switchTab(event, 'env-certifications')">Certifications</button>
                        <button class="tab-btn" onclick="switchTab(event, 'env-recommendations')">Améliorations</button>
                    </div>
                    <div class="popup-body">
                        <div id="env-overview" class="tab-content active">
                            <div class="metrics-grid">
                                <div class="metric-card">
                                    <div class="metric-value">${result.data.carbon_footprint || 245.7} t</div>
                                    <div class="metric-label">Empreinte Carbone</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value">${result.data.sustainability_score || 78}/100</div>
                                    <div class="metric-label">Score Durabilité</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value">${result.data.energy_efficiency || 'A+'}</div>
                                    <div class="metric-label">Efficacité Énergétique</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value">${result.data.renewable_energy || 65}%</div>
                                    <div class="metric-label">Énergie Renouvelable</div>
                                </div>
                            </div>
                        </div>
                        <div id="env-certifications" class="tab-content">
                            <div class="certifications-list">
                                ${(result.data.certifications || ['LEED Gold', 'BREEAM Excellent']).map(cert => `
                                    <div class="certification-item">
                                        <i class="fas fa-certificate"></i>
                                        <span>${cert}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div id="env-recommendations" class="tab-content">
                            <div class="recommendations-list">
                                ${(result.data.recommendations || []).map(rec => `
                                    <div class="recommendation-item">
                                        <i class="fas fa-seedling"></i>
                                        <span>${rec}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;

            // Animation d'entrée
            setTimeout(() => popup.classList.add('show'), 10);
        }

        // Fonction pour afficher le pop-up d'optimisation
        function showOptimizationPopup(result) {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: var(--danger-gradient);">
                        <h2><i class="fas fa-magic"></i> Optimisation Automatique avec IA</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-tabs">
                        <button class="tab-btn active" onclick="switchTab(event, 'opt-overview')">Résultats</button>
                        <button class="tab-btn" onclick="switchTab(event, 'opt-details')">Détails</button>
                        <button class="tab-btn" onclick="switchTab(event, 'opt-roadmap')">Feuille de Route</button>
                    </div>
                    <div class="popup-body">
                        <div id="opt-overview" class="tab-content active">
                            <div class="metrics-grid">
                                <div class="metric-card">
                                    <div class="metric-value">${result.data.optimization_score || 85}/100</div>
                                    <div class="metric-label">Score d'Optimisation</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value">${result.data.potential_savings || 12.5}%</div>
                                    <div class="metric-label">Économies Potentielles</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value">${result.data.roi_estimate || 18.7}%</div>
                                    <div class="metric-label">ROI Estimé</div>
                                </div>
                            </div>
                        </div>
                        <div id="opt-details" class="tab-content">
                            <div class="optimization-details">
                                ${Object.entries(result.data.optimizations || {}).map(([category, data]) => `
                                    <div class="optimization-category">
                                        <h4>${category.charAt(0).toUpperCase() + category.slice(1)}</h4>
                                        <div class="score">Score: ${data.score}/100</div>
                                        <ul>
                                            ${data.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                                        </ul>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div id="opt-roadmap" class="tab-content">
                            <div class="roadmap-timeline">
                                ${(result.data.implementation_roadmap || []).map(phase => `
                                    <div class="timeline-item">
                                        <div class="timeline-marker">Phase ${phase.phase}</div>
                                        <div class="timeline-content">
                                            <h4>${phase.task}</h4>
                                            <span class="duration">${phase.duration}</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;

            // Animation d'entrée
            setTimeout(() => popup.classList.add('show'), 10);
        }

        // Fonctions utilitaires pour les pop-ups
        function closeCurrentPopup() {
            if (currentPopup) {
                currentPopup.classList.add('hide');
                setTimeout(() => {
                    currentPopup.remove();
                    currentPopup = null;
                }, 300);
            }
        }

        function switchTab(event, tabId) {
            // Désactiver tous les onglets
            event.target.parentElement.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Masquer tous les contenus
            event.target.closest('.popup-content').querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tabId).classList.add('active');
        }

        // Fonction pour afficher le pop-up des anomalies
        function showAnomaliesPopup(result) {
            if (currentPopup) currentPopup.remove();

            const summary = result.summary;
            const anomalies = result.anomalies;

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                        <h2><i class="fas fa-exclamation-triangle"></i> Anomalies Détectées</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-tabs">
                        <button class="tab-btn active" onclick="switchTab(event, 'anomalies-overview')">Vue d'ensemble</button>
                        <button class="tab-btn" onclick="switchTab(event, 'anomalies-details')">Détails (${anomalies.length})</button>
                        <button class="tab-btn" onclick="switchTab(event, 'anomalies-solutions')">Solutions</button>
                    </div>
                    <div class="popup-body">
                        <div id="anomalies-overview" class="tab-content active">
                            <div class="metrics-grid">
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #dc2626;">${summary.total_anomalies}</div>
                                    <div class="metric-label">Total Anomalies</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #dc2626;">${summary.by_severity.critical}</div>
                                    <div class="metric-label">Critiques</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #f59e0b;">${summary.by_severity.high}</div>
                                    <div class="metric-label">Élevées</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #10b981;">${summary.by_severity.medium}</div>
                                    <div class="metric-label">Moyennes</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #6b7280;">${summary.by_severity.low}</div>
                                    <div class="metric-label">Faibles</div>
                                </div>
                            </div>

                            ${summary.total_anomalies === 0 ? `
                                <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, #d1fae5, #a7f3d0); border-radius: 12px; margin-top: 20px;">
                                    <i class="fas fa-check-circle" style="font-size: 3em; color: #059669; margin-bottom: 15px;"></i>
                                    <h3 style="color: #065f46; margin-bottom: 10px;">Excellent !</h3>
                                    <p style="color: #047857; margin: 0;">Aucune anomalie détectée. Votre modèle BIM est de très bonne qualité.</p>
                                </div>
                            ` : `
                                <div style="background: #fef2f2; padding: 20px; border-radius: 12px; margin-top: 20px; border-left: 4px solid #dc2626;">
                                    <h4 style="color: #dc2626; margin-bottom: 10px;">⚠️ Attention requise</h4>
                                    <p style="color: #991b1b; margin: 0;">
                                        ${summary.total_anomalies} anomalie(s) détectée(s) dans votre modèle BIM.
                                        ${summary.by_severity.critical > 0 ? 'Des anomalies critiques nécessitent une correction immédiate.' :
                                          summary.by_severity.high > 0 ? 'Des anomalies importantes doivent être corrigées.' :
                                          'La plupart des anomalies sont mineures mais méritent attention.'}
                                    </p>
                                </div>
                            `}
                        </div>

                        <div id="anomalies-details" class="tab-content">
                            <div style="max-height: 400px; overflow-y: auto;">
                                ${anomalies.length > 0 ? anomalies.map((anomaly, index) => `
                                    <div class="anomaly-item anomaly-${anomaly.severity}" style="margin-bottom: 15px; position: relative;">
                                        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.1); padding: 4px 8px; border-radius: 12px; font-size: 0.8em; color: #666; font-weight: 500;">
                                            #${index + 1}
                                        </div>
                                        <div style="margin-bottom: 8px;">
                                            <strong style="color: #1e293b;">${anomaly.element_type}</strong>
                                            <div style="font-size: 0.9em; color: #64748b; margin-top: 2px;">${anomaly.element_name}</div>
                                        </div>
                                        <div style="margin-bottom: 8px;">
                                            <span class="status-indicator status-${anomaly.severity === 'critical' ? 'error' : anomaly.severity === 'high' ? 'warning' : 'info'}" style="margin-right: 8px;"></span>
                                            ${anomaly.description}
                                        </div>
                                        <div style="background: rgba(59, 130, 246, 0.1); padding: 8px 12px; border-radius: 6px; font-size: 0.9em;">
                                            <strong style="color: #2563eb;">💡 Solution:</strong>
                                            <span style="color: #1e40af;">${anomaly.suggested_fix}</span>
                                        </div>
                                    </div>
                                `).join('') : '<p>Aucune anomalie détectée.</p>'}
                            </div>
                        </div>

                        <div id="anomalies-solutions" class="tab-content">
                            <div class="recommendations-list">
                                ${summary.total_anomalies > 0 ? `
                                    <div class="recommendation-item">
                                        <i class="fas fa-tools" style="color: #3b82f6;"></i>
                                        <span><strong>Prioriser les corrections:</strong> Commencez par les anomalies critiques, puis les élevées.</span>
                                    </div>
                                    <div class="recommendation-item">
                                        <i class="fas fa-search" style="color: #10b981;"></i>
                                        <span><strong>Vérification manuelle:</strong> Contrôlez visuellement les éléments signalés dans votre logiciel BIM.</span>
                                    </div>
                                    <div class="recommendation-item">
                                        <i class="fas fa-sync-alt" style="color: #f59e0b;"></i>
                                        <span><strong>Re-analyse:</strong> Lancez une nouvelle détection après corrections pour valider les améliorations.</span>
                                    </div>
                                    <div class="recommendation-item">
                                        <i class="fas fa-file-alt" style="color: #8b5cf6;"></i>
                                        <span><strong>Documentation:</strong> Documentez les corrections apportées pour le suivi qualité.</span>
                                    </div>
                                ` : `
                                    <div class="recommendation-item">
                                        <i class="fas fa-thumbs-up" style="color: #10b981;"></i>
                                        <span><strong>Modèle de qualité:</strong> Votre modèle BIM respecte les bonnes pratiques.</span>
                                    </div>
                                    <div class="recommendation-item">
                                        <i class="fas fa-shield-alt" style="color: #3b82f6;"></i>
                                        <span><strong>Maintenance:</strong> Continuez les vérifications régulières pour maintenir cette qualité.</span>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;

            // Animation d'entrée
            setTimeout(() => popup.classList.add('show'), 10);
        }

        // 🚀 NOUVELLES FONCTIONS DE POP-UPS POUR TOUTES LES ANALYSES

        // Pop-up pour l'analyse complète
        function showAnalysisPopup(analysis) {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <h2><i class="fas fa-chart-bar"></i> Analyse Complète du Modèle BIM</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-tabs">
                        <button class="tab-btn active" onclick="switchTab(event, 'analysis-metrics')">📊 Métriques</button>
                        <button class="tab-btn" onclick="switchTab(event, 'analysis-project')">🏢 Projet</button>
                        <button class="tab-btn" onclick="switchTab(event, 'analysis-details')">📋 Détails</button>
                    </div>
                    <div class="popup-body">
                        <div id="analysis-metrics" class="tab-content active">
                            ${generateMetricsContent(analysis)}
                        </div>
                        <div id="analysis-project" class="tab-content">
                            ${generateProjectContent(analysis)}
                        </div>
                        <div id="analysis-details" class="tab-content">
                            ${generateDetailsContent(analysis)}
                        </div>
                    </div>
                    <div style="padding: 20px; border-top: 1px solid #e2e8f0; display: flex; gap: 15px; justify-content: flex-end;">
                        <button onclick="closeCurrentPopup()" style="background: #f1f5f9; color: #64748b; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500;">
                            Fermer
                        </button>
                        <button onclick="generateReport()" style="background: var(--primary-gradient); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-file-pdf"></i>
                            Générer le Rapport
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        // Pop-up pour la classification
        function showClassificationPopup(result) {
            if (currentPopup) currentPopup.remove();

            const classification = result.classification;
            const features = result.features;
            const indicators = result.type_indicators;

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <h2><i class="fas fa-building"></i> Classification IA du Bâtiment</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-tabs">
                        <button class="tab-btn active" onclick="switchTab(event, 'class-result')">🏢 Résultat</button>
                        <button class="tab-btn" onclick="switchTab(event, 'class-features')">📊 Caractéristiques</button>
                        <button class="tab-btn" onclick="switchTab(event, 'class-details')">🔍 Détails IA</button>
                    </div>
                    <div class="popup-body">
                        <div id="class-result" class="tab-content active">
                            ${classification ? `
                                <div style="text-align: center; margin-bottom: 30px;">
                                    <div style="font-size: 2.5em; margin: 20px 0; color: #1e40af;">${classification.building_type || 'Type non déterminé'}</div>
                                    <div style="font-size: 1.2em; color: #059669; font-weight: bold; margin-bottom: 15px;">
                                        Confiance: ${(classification.confidence * 100).toFixed(1)}%
                                    </div>
                                    <div style="width: 80%; height: 12px; background: #e5e7eb; border-radius: 6px; overflow: hidden; margin: 0 auto;">
                                        <div style="width: ${(classification.confidence * 100).toFixed(1)}%; height: 100%; background: linear-gradient(90deg, #10b981, #059669); transition: width 0.3s ease;"></div>
                                    </div>
                                </div>
                                <div class="metrics-grid">
                                    <div class="metric-card">
                                        <div class="metric-value" style="color: #3b82f6;">${classification.classification_method || 'BIMEX IA'}</div>
                                        <div class="metric-label">Méthode de Classification</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value" style="color: #059669;">${(classification.confidence * 100).toFixed(1)}%</div>
                                        <div class="metric-label">Niveau de Confiance</div>
                                    </div>
                                </div>
                            ` : '<p>Aucune donnée de classification disponible.</p>'}
                        </div>
                        <div id="class-features" class="tab-content">
                            ${features ? `
                                <div class="metrics-grid">
                                    <div class="metric-card">
                                        <div class="metric-value">${Math.round(features.total_floor_area || 0).toLocaleString()}</div>
                                        <div class="metric-label">Surface totale (m²)</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">${features.total_storeys || 0}</div>
                                        <div class="metric-label">Étages</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">${(features.window_wall_ratio * 100 || 0).toFixed(1)}%</div>
                                        <div class="metric-label">Ratio fenêtres/murs</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">${Math.round(features.complexity_score || 0)}</div>
                                        <div class="metric-label">Score de complexité</div>
                                    </div>
                                </div>
                            ` : '<p>Aucune caractéristique extraite disponible.</p>'}
                        </div>
                        <div id="class-details" class="tab-content">
                            ${indicators ? `
                                <div style="display: flex; flex-direction: column; gap: 15px;">
                                    ${Object.entries(indicators).map(([type, score]) => {
                                        const color = score === 'Élevé' ? '#27ae60' : score === 'Moyen' ? '#f39c12' : '#95a5a6';
                                        return `
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">
                                                <span style="font-weight: 500;">${type.replace('_score', '').replace('_', ' ').toUpperCase()}</span>
                                                <span style="color: ${color}; font-weight: bold; padding: 4px 12px; background: white; border-radius: 12px;">${score}</span>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            ` : '<p>Aucun indicateur de type disponible.</p>'}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        // Pop-up pour l'analyse PMR
        function showPMRPopup(pmrAnalysis) {
            if (currentPopup) currentPopup.remove();

            const summary = pmrAnalysis.summary;
            const checks = pmrAnalysis.pmr_checks;

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #059669, #047857);">
                        <h2><i class="fas fa-wheelchair"></i> Analyse PMR (Accessibilité)</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-tabs">
                        <button class="tab-btn active" onclick="switchTab(event, 'pmr-summary')">📊 Résumé</button>
                        <button class="tab-btn" onclick="switchTab(event, 'pmr-checks')">✅ Vérifications</button>
                        <button class="tab-btn" onclick="switchTab(event, 'pmr-recommendations')">💡 Recommandations</button>
                    </div>
                    <div class="popup-body">
                        <div id="pmr-summary" class="tab-content active">
                            <div class="metrics-grid">
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #059669;">${summary.total_checks}</div>
                                    <div class="metric-label">Vérifications Totales</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #10b981;">${summary.compliant_checks}</div>
                                    <div class="metric-label">Conformes</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #dc2626;">${summary.non_compliant_checks}</div>
                                    <div class="metric-label">Non Conformes</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value" style="color: #f59e0b;">${summary.attention_checks}</div>
                                    <div class="metric-label">Attention</div>
                                </div>
                                <div class="metric-card">
                                    <div class="metric-value" style="color: ${summary.compliance_percentage >= 80 ? '#10b981' : summary.compliance_percentage >= 60 ? '#f59e0b' : '#dc2626'};">${summary.compliance_percentage.toFixed(1)}%</div>
                                    <div class="metric-label">Taux de Conformité</div>
                                </div>
                            </div>

                            ${summary.compliance_percentage >= 80 ? `
                                <div style="background: linear-gradient(135deg, #d1fae5, #a7f3d0); padding: 20px; border-radius: 12px; margin-top: 20px; text-align: center;">
                                    <i class="fas fa-check-circle" style="font-size: 2em; color: #059669; margin-bottom: 10px;"></i>
                                    <h3 style="color: #065f46; margin-bottom: 10px;">Excellente conformité PMR !</h3>
                                    <p style="color: #047857; margin: 0;">Le bâtiment respecte très bien les normes d'accessibilité.</p>
                                </div>
                            ` : `
                                <div style="background: linear-gradient(135deg, #fef2f2, #fee2e2); padding: 20px; border-radius: 12px; margin-top: 20px; text-align: center;">
                                    <i class="fas fa-exclamation-triangle" style="font-size: 2em; color: #dc2626; margin-bottom: 10px;"></i>
                                    <h3 style="color: #991b1b; margin-bottom: 10px;">Améliorations nécessaires</h3>
                                    <p style="color: #b91c1c; margin: 0;">Des corrections sont recommandées pour améliorer l'accessibilité.</p>
                                </div>
                            `}
                        </div>

                        <div id="pmr-checks" class="tab-content">
                            <div style="max-height: 400px; overflow-y: auto;">
                                ${checks.map((check, index) => `
                                    <div style="margin-bottom: 15px; padding: 15px; background: ${getComplianceColor(check.compliance)}; border-radius: 8px; border-left: 4px solid ${getComplianceBorderColor(check.compliance)};">
                                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                            <strong style="color: #1e293b;">${check.check_name}</strong>
                                            <span style="background: ${getComplianceBadgeColor(check.compliance)}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: 500;">
                                                ${check.compliance}
                                            </span>
                                        </div>
                                        <div style="color: #475569; margin-bottom: 8px;">${check.description}</div>
                                        ${check.details ? `<div style="font-size: 0.9em; color: #64748b;"><strong>Détails:</strong> ${check.details}</div>` : ''}
                                        ${check.recommendation ? `
                                            <div style="margin-top: 10px; padding: 8px 12px; background: rgba(59, 130, 246, 0.1); border-radius: 6px;">
                                                <strong style="color: #2563eb;">💡 Recommandation:</strong>
                                                <span style="color: #1e40af;">${check.recommendation}</span>
                                            </div>
                                        ` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div id="pmr-recommendations" class="tab-content">
                            <div class="recommendations-list">
                                <div class="recommendation-item">
                                    <i class="fas fa-ruler" style="color: #3b82f6;"></i>
                                    <span><strong>Largeurs de passage:</strong> Vérifiez que toutes les portes font au moins 80cm de large.</span>
                                </div>
                                <div class="recommendation-item">
                                    <i class="fas fa-stairs" style="color: #10b981;"></i>
                                    <span><strong>Rampes d'accès:</strong> Pente maximale de 5% pour les rampes extérieures.</span>
                                </div>
                                <div class="recommendation-item">
                                    <i class="fas fa-expand-arrows-alt" style="color: #f59e0b;"></i>
                                    <span><strong>Espaces de manœuvre:</strong> Diamètre libre de 1,50m devant les équipements.</span>
                                </div>
                                <div class="recommendation-item">
                                    <i class="fas fa-restroom" style="color: #8b5cf6;"></i>
                                    <span><strong>Sanitaires adaptés:</strong> Au moins un WC accessible par niveau.</span>
                                </div>
                                <div class="recommendation-item">
                                    <i class="fas fa-sign" style="color: #06b6d4;"></i>
                                    <span><strong>Signalétique:</strong> Contrastes visuels et informations en braille.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        // Fonctions utilitaires pour les couleurs PMR
        function getComplianceColor(compliance) {
            switch(compliance) {
                case 'CONFORME': return '#d5f4e6';
                case 'NON_CONFORME': return '#fde8e8';
                case 'ATTENTION': return '#fef3c7';
                default: return '#f8f9fa';
            }
        }

        function getComplianceBorderColor(compliance) {
            switch(compliance) {
                case 'CONFORME': return '#10b981';
                case 'NON_CONFORME': return '#dc2626';
                case 'ATTENTION': return '#f59e0b';
                default: return '#e2e8f0';
            }
        }

        function getComplianceBadgeColor(compliance) {
            switch(compliance) {
                case 'CONFORME': return '#10b981';
                case 'NON_CONFORME': return '#dc2626';
                case 'ATTENTION': return '#f59e0b';
                default: return '#6b7280';
            }
        }

        // Fonctions utilitaires pour générer le contenu des onglets
        function generateMetricsContent(analysis) {
            // 🔧 CORRECTION: Explorer toutes les structures possibles
            console.log('🔍 ANALYSE COMPLÈTE - Structure reçue:', analysis);

            // Essayer différentes structures possibles - CORRECTION: analysis_results contient directement les données
            let data = analysis.analysis_results ||
                      analysis.data ||
                      analysis.metrics ||
                      analysis;

            console.log('🔍 Données extraites pour métriques:', data);

            // Essayer de trouver les métriques dans différentes structures
            const metrics = data.metrics || data.building_metrics || {};
            const surfaces = metrics.surfaces || metrics.surface_metrics || {};
            const elements = metrics.elements || metrics.element_counts || {};
            const storeys = metrics.storeys || metrics.storey_info || {};
            const spaces = metrics.spaces || metrics.space_info || {};

            // Logs détaillés pour debug
            console.log('🔍 Métriques trouvées:', { metrics, surfaces, elements, storeys, spaces });

            // Essayer d'extraire les valeurs de différentes façons
            const totalFloorArea = surfaces.total_floor_area ||
                                 surfaces.floor_area ||
                                 metrics.total_floor_area ||
                                 0;

            const totalStoreys = storeys.total_storeys ||
                               storeys.count ||
                               metrics.total_storeys ||
                               0;

            const totalSpaces = spaces.total_spaces ||
                              spaces.count ||
                              metrics.total_spaces ||
                              0;

            const wallCount = elements.walls ||
                            elements.wall_count ||
                            metrics.wall_count ||
                            0;

            const doorCount = elements.doors ||
                            elements.door_count ||
                            metrics.door_count ||
                            0;

            const windowCount = elements.windows ||
                              elements.window_count ||
                              metrics.window_count ||
                              0;

            return `
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">${Math.round(totalFloorArea).toLocaleString()}</div>
                        <div class="metric-label">Surface totale (m²)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${totalStoreys}</div>
                        <div class="metric-label">Nombre d'étages</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${totalSpaces}</div>
                        <div class="metric-label">Nombre d'espaces</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${wallCount}</div>
                        <div class="metric-label">Murs</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${doorCount}</div>
                        <div class="metric-label">Portes</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${windowCount}</div>
                        <div class="metric-label">Fenêtres</div>
                    </div>
                </div>

                <h4 style="margin-top: 30px; color: #374151;">Détails des surfaces:</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">${Math.round(surfaces.total_wall_area || surfaces.wall_area || 0).toLocaleString()}</div>
                        <div class="metric-label">Surface des murs (m²)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${Math.round(surfaces.total_window_area || surfaces.window_area || 0).toLocaleString()}</div>
                        <div class="metric-label">Surface des fenêtres (m²)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${Math.round(surfaces.total_door_area || surfaces.door_area || 0).toLocaleString()}</div>
                        <div class="metric-label">Surface des portes (m²)</div>
                    </div>
                </div>

                <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin-top: 20px;">
                    <h5 style="color: #1e40af; margin-bottom: 10px;">🔍 Debug - Valeurs trouvées:</h5>
                    <div style="font-size: 0.8em; color: #1e40af;">
                        <p>Surface: ${totalFloorArea} | Étages: ${totalStoreys} | Espaces: ${totalSpaces}</p>
                        <p>Murs: ${wallCount} | Portes: ${doorCount} | Fenêtres: ${windowCount}</p>
                    </div>
                </div>
            `;
        }

        function generateProjectContent(analysis) {
            // 🔧 CORRECTION: Explorer toutes les structures possibles
            console.log('🔍 PROJET - Structure reçue:', analysis);

            let data = analysis.analysis_results ||
                      analysis.data ||
                      analysis;

            const projectInfo = data.project_info || data.project || {};

            console.log('🔍 Données projet extraites:', { data, projectInfo });

            // Essayer d'extraire les informations de différentes façons
            const projectName = projectInfo.project_name ||
                              projectInfo.name ||
                              data.project_name ||
                              'basic2';

            const projectDescription = projectInfo.project_description ||
                                     projectInfo.description ||
                                     data.project_description ||
                                     'Projet BIM analysé';

            const totalElements = projectInfo.total_elements ||
                                data.total_elements ||
                                data.element_count ||
                                'Non calculé';

            return `
                <div style="background: #f8fafc; padding: 20px; border-radius: 12px;">
                    <h4 style="color: #374151; margin-bottom: 20px;">Informations du projet:</h4>
                    <div style="display: grid; gap: 15px;">
                        <div style="display: flex; justify-content: space-between; padding: 10px; background: white; border-radius: 8px;">
                            <strong>Nom:</strong>
                            <span>${projectName}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 10px; background: white; border-radius: 8px;">
                            <strong>Description:</strong>
                            <span>${projectDescription}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 10px; background: white; border-radius: 8px;">
                            <strong>Phase:</strong>
                            <span>${projectInfo.project_phase || 'Analyse'}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 10px; background: white; border-radius: 8px;">
                            <strong>Bâtiment:</strong>
                            <span>${projectInfo.building_name || 'Bâtiment principal'}</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 10px; background: white; border-radius: 8px;">
                            <strong>Schéma IFC:</strong>
                            <span>IFC2X3</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; padding: 10px; background: white; border-radius: 8px;">
                            <strong>Éléments totaux:</strong>
                            <span>${typeof totalElements === 'number' ? totalElements.toLocaleString() : totalElements}</span>
                        </div>
                    </div>

                    <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin-top: 20px;">
                        <h5 style="color: #1e40af; margin-bottom: 10px;">🔍 Debug - Données projet:</h5>
                        <div style="font-size: 0.8em; color: #1e40af;">
                            <p>Nom trouvé: ${projectName}</p>
                            <p>Éléments: ${totalElements}</p>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateDetailsContent(analysis) {
            // 🔧 CORRECTION: Extraire les données de la bonne structure
            const data = analysis.analysis_results || analysis.data || analysis;
            const materials = data.metrics?.materials || {};
            const anomalies = data.anomalies || {};
            const classification = data.classification || {};
            const pmr = data.pmr_analysis || {};

            console.log('🔍 Données détails extraites:', { data, materials, anomalies, classification, pmr });

            return `
                <h4 style="color: #374151; margin-bottom: 20px;">Matériaux détectés:</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">${materials.total_materials || 0}</div>
                        <div class="metric-label">Types de matériaux</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${Math.round(materials.concrete_volume || 0).toLocaleString()}</div>
                        <div class="metric-label">Volume béton (m³)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${Math.round(materials.steel_weight || 0).toLocaleString()}</div>
                        <div class="metric-label">Poids acier (kg)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${Math.round(materials.wood_volume || 0).toLocaleString()}</div>
                        <div class="metric-label">Volume bois (m³)</div>
                    </div>
                </div>

                <h4 style="color: #374151; margin-top: 30px; margin-bottom: 20px;">Résumé des analyses:</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" style="color: #dc2626;">${anomalies.summary?.total_anomalies || 0}</div>
                        <div class="metric-label">Anomalies détectées</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" style="color: #3b82f6;">${classification.building_type || 'Non classifié'}</div>
                        <div class="metric-label">Type de bâtiment</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" style="color: #059669;">${pmr.summary?.compliance_percentage?.toFixed(1) || 0}%</div>
                        <div class="metric-label">Conformité PMR</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" style="color: #8b5cf6;">4</div>
                        <div class="metric-label">Modules analysés</div>
                    </div>
                </div>

                <div style="background: #f0f9ff; padding: 20px; border-radius: 12px; margin-top: 30px;">
                    <h4 style="color: #1e40af; margin-bottom: 15px;">📊 Analyse technique:</h4>
                    <div style="color: #1e40af;">
                        <p>• Modèle BIM analysé avec succès</p>
                        <p>• Extraction automatique des métriques</p>
                        <p>• Calculs de surfaces et volumes</p>
                        <p>• Identification des éléments structurels</p>
                        <p>• Analyse des matériaux et propriétés</p>
                        <p>• Classification IA du bâtiment</p>
                        <p>• Vérification conformité PMR</p>
                        <p>• Détection d'anomalies qualité</p>
                    </div>
                </div>
            `;
        }

        // 🚀 NOUVELLES FONCTIONS AVEC ÉTAT DE CHARGEMENT

        // Pop-up d'analyse avec chargement
        function showAnalysisPopupWithLoading() {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <h2><i class="fas fa-chart-bar"></i> Analyse Complète du Modèle BIM</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body" style="text-align: center; padding: 60px;">
                        <div class="loading-spinner" style="margin: 0 auto 20px auto;"></div>
                        <h3 style="color: #3b82f6; margin-bottom: 10px;">Analyse en cours...</h3>
                        <p style="color: #64748b;">Extraction des métriques, analyse des éléments et génération du rapport.</p>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function updateAnalysisPopup(analysis) {
            if (!currentPopup) return;

            // 🔧 DEBUG: Afficher la structure des données reçues
            console.log('🔍 Données d\'analyse reçues dans updateAnalysisPopup:', analysis);

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                    <h2><i class="fas fa-chart-bar"></i> Analyse Complète du Modèle BIM</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-tabs">
                    <button class="tab-btn active" onclick="switchTab(event, 'analysis-metrics')">📊 Métriques</button>
                    <button class="tab-btn" onclick="switchTab(event, 'analysis-project')">🏢 Projet</button>
                    <button class="tab-btn" onclick="switchTab(event, 'analysis-details')">📋 Détails</button>
                    <button class="tab-btn" onclick="switchTab(event, 'analysis-debug')">🔍 Debug</button>
                </div>
                <div class="popup-body">
                    <div id="analysis-metrics" class="tab-content active">
                        ${generateMetricsContent(analysis)}
                    </div>
                    <div id="analysis-project" class="tab-content">
                        ${generateProjectContent(analysis)}
                    </div>
                    <div id="analysis-details" class="tab-content">
                        ${generateDetailsContent(analysis)}
                    </div>
                    <div id="analysis-debug" class="tab-content">
                        <h4>🔍 Structure des données reçues:</h4>
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 8px; overflow: auto; max-height: 400px; font-size: 0.8em;">${JSON.stringify(analysis, null, 2)}</pre>
                    </div>
                </div>
                <div style="padding: 20px; border-top: 1px solid #e2e8f0; display: flex; gap: 15px; justify-content: flex-end;">
                    <button onclick="closeCurrentPopup()" style="background: #f1f5f9; color: #64748b; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500;">
                        Fermer
                    </button>
                    <button onclick="generateReport()" style="background: var(--primary-gradient); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 500; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-file-pdf"></i>
                        Générer le Rapport
                    </button>
                </div>
            `;
        }

        function updateAnalysisPopupWithError(errorMessage) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Erreur d'Analyse</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-times-circle" style="font-size: 3em; color: #dc2626; margin-bottom: 20px;"></i>
                    <h3 style="color: #dc2626; margin-bottom: 15px;">Erreur lors de l'analyse</h3>
                    <div style="background: #fef2f2; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                        <code style="color: #991b1b; font-size: 0.9em;">${errorMessage}</code>
                    </div>
                    <p style="color: #64748b;">Vérifiez que le backend est démarré et que le fichier IFC est valide.</p>
                </div>
            `;
        }

        // Pop-up de classification avec chargement
        function showClassificationPopupWithLoading() {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <h2><i class="fas fa-building"></i> Classification IA du Bâtiment</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body" style="text-align: center; padding: 60px;">
                        <div class="loading-spinner" style="margin: 0 auto 20px auto;"></div>
                        <h3 style="color: #3b82f6; margin-bottom: 10px;">Classification en cours...</h3>
                        <p style="color: #64748b;">Analyse IA des caractéristiques du bâtiment et classification automatique.</p>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function updateClassificationPopup(result) {
            if (!currentPopup) return;

            // Remplacer le contenu du pop-up existant par les vraies données
            showClassificationPopup(result);
        }

        function updateClassificationPopupWithError(errorMessage) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Erreur de Classification</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-times-circle" style="font-size: 3em; color: #dc2626; margin-bottom: 20px;"></i>
                    <h3 style="color: #dc2626; margin-bottom: 15px;">Erreur lors de la classification</h3>
                    <div style="background: #fef2f2; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                        <code style="color: #991b1b; font-size: 0.9em;">${errorMessage}</code>
                    </div>
                    <p style="color: #64748b;">Vérifiez que le backend est démarré et que le fichier IFC est valide.</p>
                </div>
            `;
        }

        // Pop-up d'anomalies avec chargement
        function showAnomaliesPopupWithLoading() {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                        <h2><i class="fas fa-exclamation-triangle"></i> Détection d'Anomalies</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body" style="text-align: center; padding: 60px;">
                        <div class="loading-spinner" style="margin: 0 auto 20px auto;"></div>
                        <h3 style="color: #dc2626; margin-bottom: 10px;">Détection en cours...</h3>
                        <p style="color: #64748b;">Analyse du modèle BIM pour détecter les anomalies et problèmes de qualité.</p>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function updateAnomaliesPopup(result) {
            if (!currentPopup) return;

            // Remplacer le contenu du pop-up existant par les vraies données
            showAnomaliesPopup(result);
        }

        function updateAnomaliesPopupWithError(errorMessage) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Erreur de Détection</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-times-circle" style="font-size: 3em; color: #dc2626; margin-bottom: 20px;"></i>
                    <h3 style="color: #dc2626; margin-bottom: 15px;">Erreur lors de la détection</h3>
                    <div style="background: #fef2f2; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                        <code style="color: #991b1b; font-size: 0.9em;">${errorMessage}</code>
                    </div>
                    <p style="color: #64748b;">Vérifiez que le backend est démarré et que le fichier IFC est valide.</p>
                </div>
            `;
        }

        // Pop-up PMR avec chargement
        function showPMRPopupWithLoading() {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #059669, #047857);">
                        <h2><i class="fas fa-wheelchair"></i> Analyse PMR (Accessibilité)</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body" style="text-align: center; padding: 60px;">
                        <div class="loading-spinner" style="margin: 0 auto 20px auto;"></div>
                        <h3 style="color: #059669; margin-bottom: 10px;">Analyse PMR en cours...</h3>
                        <p style="color: #64748b;">Vérification de la conformité aux normes d'accessibilité pour personnes à mobilité réduite.</p>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function updatePMRPopup(pmrAnalysis) {
            if (!currentPopup) return;

            console.log('🔍 DONNÉES PMR COMPLÈTES reçues:', pmrAnalysis);
            console.log('🔍 Type de pmrAnalysis:', typeof pmrAnalysis);
            console.log('🔍 Clés disponibles:', Object.keys(pmrAnalysis));

            // 🔧 CORRECTION: Explorer TOUTES les structures possibles pour PMR
            let data = pmrAnalysis.analysis ||
                      pmrAnalysis.data ||
                      pmrAnalysis.pmr_analysis ||
                      pmrAnalysis.results ||
                      pmrAnalysis;

            console.log('🔍 Data extraite:', data);
            console.log('🔍 Type de data:', typeof data);
            console.log('🔍 Clés de data:', Object.keys(data));

            const summary = data.summary || data.pmr_summary || {};
            const checks = data.pmr_checks ||
                          data.checks ||
                          data.verifications ||
                          data.pmr_verifications ||
                          [];

            console.log('🔍 Summary trouvé:', summary);
            console.log('🔍 Checks trouvés:', checks);
            console.log('🔍 Nombre de checks:', checks.length);

            // 🔧 CORRECTION: Extraire les métriques du summary d'abord
            let totalChecks = summary.total_checks || checks.length || 0;
            let compliantChecks = summary.compliance_counts?.conforme || summary.compliant_checks || 0;
            let nonCompliantChecks = summary.compliance_counts?.non_conforme || summary.non_compliant_checks || 0;
            let attentionChecks = summary.compliance_counts?.attention || summary.attention_checks || 0;
            let compliancePercentage = summary.conformity_score || summary.compliance_percentage || 0;

            // Si les métriques ne sont pas dans summary, les calculer à partir des checks
            if (totalChecks === 0 && checks.length > 0) {
                console.log('🔧 Calcul des métriques à partir des checks...');
                totalChecks = checks.length;

                // Examiner chaque check pour debug
                checks.forEach((check, index) => {
                    console.log(`🔍 Check ${index + 1} COMPLET:`, check);
                    console.log(`  - compliance: "${check.compliance}" (type: ${typeof check.compliance})`);
                    console.log(`  - check_name: "${check.check_name}"`);
                    console.log(`  - status: "${check.status}"`);
                    console.log(`  - result: "${check.result}"`);
                    console.log(`  - passed: "${check.passed}"`);
                    console.log(`  - Toutes les clés:`, Object.keys(check));
                });

                compliantChecks = checks.filter(check => {
                    const compliance = (check.compliance_level || check.compliance || '').toString().toLowerCase();
                    console.log(`🔍 Vérification compliance_level "${compliance}" (original: "${check.compliance_level}"):`,
                        compliance === 'conforme' || compliance === 'compliant' || compliance === 'ok' || compliance === 'pass');
                    return compliance === 'conforme' || compliance === 'compliant' || compliance === 'ok' || compliance === 'pass';
                }).length;

                nonCompliantChecks = checks.filter(check => {
                    const compliance = (check.compliance_level || check.compliance || '').toString().toLowerCase();
                    console.log(`🔍 Vérification compliance_level "${compliance}" (original: "${check.compliance_level}"):`,
                        compliance === 'non_conforme' || compliance === 'non-conforme' || compliance === 'non_compliant' || compliance === 'fail' || compliance === 'failed');
                    return compliance === 'non_conforme' || compliance === 'non-conforme' || compliance === 'non_compliant' || compliance === 'fail' || compliance === 'failed';
                }).length;

                attentionChecks = checks.filter(check => {
                    const compliance = (check.compliance_level || check.compliance || '').toString().toLowerCase();
                    console.log(`🔍 Vérification compliance_level "${compliance}" (original: "${check.compliance_level}"):`,
                        compliance === 'attention' || compliance === 'warning' || compliance === 'warn' || compliance === 'partial');
                    return compliance === 'attention' || compliance === 'warning' || compliance === 'warn' || compliance === 'partial';
                }).length;

                compliancePercentage = totalChecks > 0 ? (compliantChecks / totalChecks) * 100 : 0;

                console.log('🔧 Métriques calculées:', {
                    totalChecks, compliantChecks, nonCompliantChecks, attentionChecks, compliancePercentage
                });
            }

            console.log('🔍 MÉTRIQUES FINALES PMR:', {
                totalChecks, compliantChecks, nonCompliantChecks, attentionChecks, compliancePercentage
            });

            // Mettre à jour le contenu du pop-up existant
            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #059669, #047857);">
                    <h2><i class="fas fa-wheelchair"></i> Analyse PMR (Accessibilité)</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-tabs">
                    <button class="tab-btn active" onclick="switchTab(event, 'pmr-summary')">📊 Résumé</button>
                    <button class="tab-btn" onclick="switchTab(event, 'pmr-checks')">✅ Vérifications</button>
                    <button class="tab-btn" onclick="switchTab(event, 'pmr-recommendations')">💡 Recommandations</button>
                </div>
                <div class="popup-body">
                    <div id="pmr-summary" class="tab-content active">
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-value" style="color: #059669;">${totalChecks}</div>
                                <div class="metric-label">Vérifications Totales</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: #10b981;">${compliantChecks}</div>
                                <div class="metric-label">Conformes</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: #dc2626;">${nonCompliantChecks}</div>
                                <div class="metric-label">Non Conformes</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: #f59e0b;">${attentionChecks}</div>
                                <div class="metric-label">Attention</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: ${compliancePercentage >= 80 ? '#10b981' : compliancePercentage >= 60 ? '#f59e0b' : '#dc2626'};">${compliancePercentage.toFixed(1)}%</div>
                                <div class="metric-label">Taux de Conformité</div>
                            </div>
                        </div>

                        ${compliancePercentage >= 80 ? `
                            <div style="background: linear-gradient(135deg, #d1fae5, #a7f3d0); padding: 20px; border-radius: 12px; margin-top: 20px; text-align: center;">
                                <i class="fas fa-check-circle" style="font-size: 2em; color: #059669; margin-bottom: 10px;"></i>
                                <h3 style="color: #065f46; margin-bottom: 10px;">Excellente conformité PMR !</h3>
                                <p style="color: #047857; margin: 0;">Le bâtiment respecte très bien les normes d'accessibilité (${compliancePercentage.toFixed(1)}%).</p>
                            </div>
                        ` : compliancePercentage >= 60 ? `
                            <div style="background: linear-gradient(135deg, #fef3c7, #fde68a); padding: 20px; border-radius: 12px; margin-top: 20px; text-align: center;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 2em; color: #f59e0b; margin-bottom: 10px;"></i>
                                <h3 style="color: #92400e; margin-bottom: 10px;">Conformité modérée</h3>
                                <p style="color: #a16207; margin: 0;">Quelques améliorations sont recommandées (${compliancePercentage.toFixed(1)}%).</p>
                            </div>
                        ` : `
                            <div style="background: linear-gradient(135deg, #fef2f2, #fee2e2); padding: 20px; border-radius: 12px; margin-top: 20px; text-align: center;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 2em; color: #dc2626; margin-bottom: 10px;"></i>
                                <h3 style="color: #991b1b; margin-bottom: 10px;">Améliorations nécessaires</h3>
                                <p style="color: #b91c1c; margin: 0;">Des corrections importantes sont requises (${compliancePercentage.toFixed(1)}%).</p>
                            </div>
                        `}
                    </div>

                    <div id="pmr-checks" class="tab-content">
                        <div style="max-height: 400px; overflow-y: auto;">
                            ${checks.length > 0 ? checks.map((check, index) => `
                                <div style="margin-bottom: 15px; padding: 15px; background: ${getComplianceColor(check.compliance)}; border-radius: 8px; border-left: 4px solid ${getComplianceBorderColor(check.compliance)};">
                                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                        <strong style="color: #1e293b;">${check.check_name || `Vérification ${index + 1}`}</strong>
                                        <span style="background: ${getComplianceBadgeColor(check.compliance)}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: 500;">
                                            ${check.compliance || 'INCONNU'}
                                        </span>
                                    </div>
                                    <div style="color: #475569; margin-bottom: 8px;">${check.description || 'Aucune description disponible'}</div>
                                    ${check.details ? `<div style="font-size: 0.9em; color: #64748b;"><strong>Détails:</strong> ${check.details}</div>` : ''}
                                    ${check.recommendation ? `
                                        <div style="margin-top: 10px; padding: 8px 12px; background: rgba(59, 130, 246, 0.1); border-radius: 6px;">
                                            <strong style="color: #2563eb;">💡 Recommandation:</strong>
                                            <span style="color: #1e40af;">${check.recommendation}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            `).join('') : `
                                <div style="text-align: center; padding: 40px; color: #64748b;">
                                    <i class="fas fa-info-circle" style="font-size: 2em; margin-bottom: 15px;"></i>
                                    <p>Aucune vérification PMR disponible.</p>
                                </div>
                            `}
                        </div>
                    </div>

                    <div id="pmr-recommendations" class="tab-content">
                        ${generatePMRRecommendations(checks, compliancePercentage, nonCompliantChecks, attentionChecks)}
                    </div>
                </div>
            `;
        }

        function updatePMRPopupWithError(errorMessage) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Erreur d'Analyse PMR</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-times-circle" style="font-size: 3em; color: #dc2626; margin-bottom: 20px;"></i>
                    <h3 style="color: #dc2626; margin-bottom: 15px;">Erreur lors de l'analyse PMR</h3>
                    <div style="background: #fef2f2; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                        <code style="color: #991b1b; font-size: 0.9em;">${errorMessage}</code>
                    </div>
                    <p style="color: #64748b;">Vérifiez que le backend est démarré et que le fichier IFC est valide.</p>
                </div>
            `;
        }

        // Ajoutons le spinner de chargement dans les styles si pas déjà présent
        if (!document.querySelector('.loading-spinner-style')) {
            const style = document.createElement('style');
            style.className = 'loading-spinner-style';
            style.textContent = `
                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #e2e8f0;
                    border-top: 4px solid #3b82f6;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // Pop-up Assistant IA avec chargement
        function showAssistantPopupWithLoading() {
            if (currentPopup) currentPopup.remove();

            const popup = document.createElement('div');
            popup.className = 'modern-popup';
            popup.innerHTML = `
                <div class="popup-content">
                    <div class="popup-header" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <h2><i class="fas fa-robot"></i> Assistant IA BIMEX</h2>
                        <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                    </div>
                    <div class="popup-body" style="text-align: center; padding: 60px;">
                        <div class="loading-spinner" style="margin: 0 auto 20px auto;"></div>
                        <h3 style="color: #8b5cf6; margin-bottom: 10px;">Chargement de l'Assistant IA...</h3>
                        <p style="color: #64748b;">Initialisation du modèle Ollama et analyse du fichier BIM.</p>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);
            currentPopup = popup;
            setTimeout(() => popup.classList.add('show'), 10);
        }

        function updateAssistantPopup(result) {
            if (!currentPopup) return;

            const summary = result.summary || {};
            const suggestedQuestions = result.suggested_questions || [];

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <h2><i class="fas fa-robot"></i> Assistant IA BIMEX</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-tabs">
                    <button class="tab-btn active" onclick="switchTab(event, 'assistant-status')">🤖 Statut</button>
                    <button class="tab-btn" onclick="switchTab(event, 'assistant-chat')">💬 Chat</button>
                    <button class="tab-btn" onclick="switchTab(event, 'assistant-suggestions')">💡 Suggestions</button>
                </div>
                <div class="popup-body">
                    <div id="assistant-status" class="tab-content active">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <i class="fas fa-check-circle" style="font-size: 3em; color: #10b981; margin-bottom: 15px;"></i>
                            <h3 style="color: #10b981; margin-bottom: 10px;">Assistant IA Chargé avec Succès !</h3>
                            <p style="color: #64748b;">L'assistant est prêt à répondre à vos questions sur le modèle BIM.</p>
                        </div>

                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-value" style="color: #8b5cf6;">${summary.project_name || 'Projet BIM'}</div>
                                <div class="metric-label">Nom du Projet</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: #3b82f6;">${summary.total_elements || 0}</div>
                                <div class="metric-label">Éléments Analysés</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: #dc2626;">${summary.total_anomalies || 0}</div>
                                <div class="metric-label">Anomalies Détectées</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: #059669;">Session ${sessionId.split('_')[1]}</div>
                                <div class="metric-label">ID de Session</div>
                            </div>
                        </div>

                        <div style="background: #f0f9ff; padding: 20px; border-radius: 12px; margin-top: 20px;">
                            <h4 style="color: #1e40af; margin-bottom: 15px;">🤖 Capacités de l'Assistant:</h4>
                            <div style="color: #1e40af;">
                                <p>• Analyse détaillée des éléments BIM</p>
                                <p>• Détection et explication des anomalies</p>
                                <p>• Conseils d'optimisation et bonnes pratiques</p>
                                <p>• Réponses en temps réel avec Ollama</p>
                                <p>• Support multilingue (français/anglais)</p>
                            </div>
                        </div>
                    </div>

                    <div id="assistant-chat" class="tab-content">
                        <div style="background: #f8fafc; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                            <h4 style="color: #374151; margin-bottom: 15px;">💬 Interface de Chat</h4>
                            <div id="chatMessages" style="height: 300px; overflow-y: auto; background: white; border-radius: 8px; padding: 15px; margin-bottom: 15px; border: 1px solid #e2e8f0;">
                                <div style="text-align: center; color: #64748b; padding: 20px;">
                                    <i class="fas fa-comments" style="font-size: 2em; margin-bottom: 10px;"></i>
                                    <p>Posez votre première question à l'Assistant IA !</p>
                                </div>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <input type="text" id="chatInput" placeholder="Posez votre question sur le modèle BIM..."
                                       style="flex: 1; padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px;"
                                       onkeypress="if(event.key==='Enter') sendMessage()">
                                <button onclick="sendMessage()" style="background: var(--primary-gradient); color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-weight: 500;">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="assistant-suggestions" class="tab-content">
                        <h4 style="color: #374151; margin-bottom: 20px;">💡 Questions Suggérées</h4>
                        <div style="display: flex; flex-direction: column; gap: 10px;">
                            ${suggestedQuestions.length > 0 ? suggestedQuestions.map(question => `
                                <button onclick="askSuggestedQuestion('${question.replace(/'/g, "\\'")}')"
                                        style="text-align: left; padding: 15px; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; cursor: pointer; transition: all 0.2s; font-size: 14px;"
                                        onmouseover="this.style.background='#e0f2fe'; this.style.borderColor='#0ea5e9';"
                                        onmouseout="this.style.background='#f8fafc'; this.style.borderColor='#e2e8f0';">
                                    <i class="fas fa-question-circle" style="color: #3b82f6; margin-right: 8px;"></i>
                                    ${question}
                                </button>
                            `).join('') : `
                                <div style="text-align: center; padding: 40px; color: #64748b;">
                                    <i class="fas fa-lightbulb" style="font-size: 2em; margin-bottom: 15px;"></i>
                                    <p>Aucune suggestion disponible pour le moment.</p>
                                    <p>Utilisez l'onglet Chat pour poser vos questions !</p>
                                </div>
                            `}
                        </div>
                    </div>
                </div>
            `;
        }

        function updateAssistantPopupWithError(errorMessage) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Erreur Assistant IA</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-times-circle" style="font-size: 3em; color: #dc2626; margin-bottom: 20px;"></i>
                    <h3 style="color: #dc2626; margin-bottom: 15px;">Erreur lors du chargement</h3>
                    <div style="background: #fef2f2; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                        <code style="color: #991b1b; font-size: 0.9em;">${errorMessage}</code>
                    </div>
                    <p style="color: #64748b;">Vérifiez que Ollama est démarré et que le modèle llama3.1:8b est installé.</p>
                </div>
            `;
        }

        function updateAssistantPopupWithWarning(result) {
            if (!currentPopup) return;

            const popupContent = currentPopup.querySelector('.popup-content');
            popupContent.innerHTML = `
                <div class="popup-header" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <h2><i class="fas fa-exclamation-triangle"></i> Assistant IA - Avertissement</h2>
                    <button class="popup-close" onclick="closeCurrentPopup()">×</button>
                </div>
                <div class="popup-body" style="text-align: center; padding: 40px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3em; color: #f59e0b; margin-bottom: 20px;"></i>
                    <h3 style="color: #d97706; margin-bottom: 15px;">Chargement avec avertissements</h3>
                    <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: left;">
                        <p style="color: #92400e;">${result.message || 'Certaines fonctionnalités peuvent être limitées.'}</p>
                    </div>
                    <p style="color: #64748b;">L'assistant fonctionne mais avec des limitations.</p>
                </div>
            `;
        }

        // Fonctions pour le chat
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (!message) return;

            const messagesContainer = document.getElementById('chatMessages');

            // Ajouter le message de l'utilisateur
            messagesContainer.innerHTML += `
                <div style="margin-bottom: 15px; text-align: right;">
                    <div style="display: inline-block; background: var(--primary-gradient); color: white; padding: 10px 15px; border-radius: 18px 18px 4px 18px; max-width: 70%;">
                        ${message}
                    </div>
                </div>
            `;

            input.value = '';
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Ajouter un indicateur de frappe
            messagesContainer.innerHTML += `
                <div id="typing-indicator" style="margin-bottom: 15px;">
                    <div style="display: inline-block; background: #f1f5f9; padding: 10px 15px; border-radius: 18px 18px 18px 4px; max-width: 70%;">
                        <i class="fas fa-circle" style="color: #3b82f6; animation: pulse 1.5s infinite;"></i>
                        <i class="fas fa-circle" style="color: #3b82f6; animation: pulse 1.5s infinite 0.2s;"></i>
                        <i class="fas fa-circle" style="color: #3b82f6; animation: pulse 1.5s infinite 0.4s;"></i>
                    </div>
                </div>
            `;
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            try {
                const response = await fetch(`${API_BASE}/assistant/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId
                    })
                });

                const result = await response.json();

                // Supprimer l'indicateur de frappe
                document.getElementById('typing-indicator')?.remove();

                if (result.status === 'success') {
                    messagesContainer.innerHTML += `
                        <div style="margin-bottom: 15px;">
                            <div style="display: inline-block; background: #f1f5f9; color: #374151; padding: 10px 15px; border-radius: 18px 18px 18px 4px; max-width: 70%;">
                                <strong style="color: #8b5cf6;">🤖 Assistant BIMEX:</strong><br>
                                ${result.response.replace(/\n/g, '<br>')}
                            </div>
                        </div>
                    `;
                } else {
                    messagesContainer.innerHTML += `
                        <div style="margin-bottom: 15px;">
                            <div style="display: inline-block; background: #fef2f2; color: #dc2626; padding: 10px 15px; border-radius: 18px 18px 18px 4px; max-width: 70%;">
                                ❌ Erreur: ${result.detail || 'Impossible de traiter votre demande'}
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('typing-indicator')?.remove();
                messagesContainer.innerHTML += `
                    <div style="margin-bottom: 15px;">
                        <div style="display: inline-block; background: #fef2f2; color: #dc2626; padding: 10px 15px; border-radius: 18px 18px 18px 4px; max-width: 70%;">
                            ❌ Erreur de connexion: ${error.message}
                        </div>
                    </div>
                `;
            }

            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function askSuggestedQuestion(question) {
            document.getElementById('chatInput').value = question;
            // Basculer vers l'onglet chat
            switchTab({ target: document.querySelector('[onclick*="assistant-chat"]') }, 'assistant-chat');
            sendMessage();
        }

        // Fonction pour générer des recommandations PMR dynamiques
        function generatePMRRecommendations(checks, compliancePercentage, nonCompliantChecks, attentionChecks) {
            const recommendations = [];

            // Analyser les checks pour identifier les problèmes spécifiques
            const problemAreas = {
                doors: false,
                ramps: false,
                spaces: false,
                toilets: false,
                signage: false,
                stairs: false,
                elevators: false,
                parking: false
            };

            // Identifier les domaines problématiques à partir des checks
            checks.forEach(check => {
                const checkName = (check.check_name || '').toLowerCase();
                const description = (check.description || '').toLowerCase();
                const compliance = check.compliance_level || check.compliance;

                if (compliance === 'non_conforme' || compliance === 'attention' || compliance === 'NON_CONFORME' || compliance === 'ATTENTION') {
                    if (checkName.includes('door') || checkName.includes('porte') || description.includes('door') || description.includes('porte')) {
                        problemAreas.doors = true;
                    }
                    if (checkName.includes('ramp') || checkName.includes('rampe') || description.includes('ramp') || description.includes('rampe')) {
                        problemAreas.ramps = true;
                    }
                    if (checkName.includes('space') || checkName.includes('espace') || description.includes('space') || description.includes('espace')) {
                        problemAreas.spaces = true;
                    }
                    if (checkName.includes('toilet') || checkName.includes('wc') || description.includes('toilet') || description.includes('wc')) {
                        problemAreas.toilets = true;
                    }
                    if (checkName.includes('sign') || checkName.includes('signal') || description.includes('sign') || description.includes('signal')) {
                        problemAreas.signage = true;
                    }
                    if (checkName.includes('stair') || checkName.includes('escalier') || description.includes('stair') || description.includes('escalier')) {
                        problemAreas.stairs = true;
                    }
                    if (checkName.includes('elevator') || checkName.includes('ascenseur') || description.includes('elevator') || description.includes('ascenseur')) {
                        problemAreas.elevators = true;
                    }
                    if (checkName.includes('parking') || description.includes('parking')) {
                        problemAreas.parking = true;
                    }
                }
            });

            // Générer des recommandations basées sur les problèmes identifiés
            if (problemAreas.doors) {
                recommendations.push({
                    icon: 'fas fa-door-open',
                    color: '#dc2626',
                    title: 'Largeurs de passage critiques',
                    description: 'Des portes ne respectent pas la largeur minimale de 80cm. Vérifiez et élargissez si nécessaire.'
                });
            }

            if (problemAreas.ramps) {
                recommendations.push({
                    icon: 'fas fa-wheelchair',
                    color: '#f59e0b',
                    title: 'Rampes d\'accès à corriger',
                    description: 'Les rampes détectées ne respectent pas les normes. Pente max 5% et largeur min 1,20m.'
                });
            }

            if (problemAreas.spaces) {
                recommendations.push({
                    icon: 'fas fa-expand-arrows-alt',
                    color: '#3b82f6',
                    title: 'Espaces de manœuvre insuffisants',
                    description: 'Prévoir un diamètre libre de 1,50m devant les équipements et dans les espaces de circulation.'
                });
            }

            if (problemAreas.toilets) {
                recommendations.push({
                    icon: 'fas fa-restroom',
                    color: '#8b5cf6',
                    title: 'Sanitaires PMR manquants',
                    description: 'Installer au moins un WC accessible par niveau avec les dimensions réglementaires.'
                });
            }

            if (problemAreas.signage) {
                recommendations.push({
                    icon: 'fas fa-sign',
                    color: '#06b6d4',
                    title: 'Signalétique à améliorer',
                    description: 'Ajouter une signalétique contrastée et des informations en braille aux points clés.'
                });
            }

            if (problemAreas.stairs) {
                recommendations.push({
                    icon: 'fas fa-stairs',
                    color: '#10b981',
                    title: 'Escaliers à sécuriser',
                    description: 'Installer des mains courantes des deux côtés et marquer les nez de marches.'
                });
            }

            if (problemAreas.elevators) {
                recommendations.push({
                    icon: 'fas fa-elevator',
                    color: '#f59e0b',
                    title: 'Ascenseurs à vérifier',
                    description: 'Contrôler les dimensions de cabine et l\'accessibilité des commandes.'
                });
            }

            if (problemAreas.parking) {
                recommendations.push({
                    icon: 'fas fa-parking',
                    color: '#6366f1',
                    title: 'Places de parking PMR',
                    description: 'Prévoir 2% de places PMR avec dimensions 3,30m x 5,00m et cheminement accessible.'
                });
            }

            // Ajouter des recommandations générales selon le taux de conformité
            if (compliancePercentage < 50) {
                recommendations.push({
                    icon: 'fas fa-exclamation-triangle',
                    color: '#dc2626',
                    title: 'Audit PMR complet recommandé',
                    description: 'Le taux de conformité est faible. Faire appel à un expert en accessibilité pour un audit détaillé.'
                });
            } else if (compliancePercentage < 80) {
                recommendations.push({
                    icon: 'fas fa-tools',
                    color: '#f59e0b',
                    title: 'Corrections prioritaires',
                    description: 'Concentrez-vous sur les non-conformités critiques avant les améliorations mineures.'
                });
            }

            // Si aucun problème spécifique détecté, ajouter des recommandations générales
            if (recommendations.length === 0) {
                recommendations.push(
                    {
                        icon: 'fas fa-check-circle',
                        color: '#10b981',
                        title: 'Maintenir la conformité',
                        description: 'Continuez les vérifications régulières pour maintenir ce bon niveau d\'accessibilité.'
                    },
                    {
                        icon: 'fas fa-eye',
                        color: '#3b82f6',
                        title: 'Contrôles visuels',
                        description: 'Effectuez des contrôles visuels réguliers pour détecter d\'éventuels problèmes.'
                    }
                );
            }

            // Toujours ajouter une recommandation sur la documentation
            recommendations.push({
                icon: 'fas fa-file-alt',
                color: '#6b7280',
                title: 'Documentation des corrections',
                description: 'Documentez toutes les corrections apportées pour le suivi et les contrôles futurs.'
            });

            // Générer le HTML
            let html = '<div class="recommendations-list">';

            recommendations.forEach(rec => {
                html += `
                    <div class="recommendation-item">
                        <i class="${rec.icon}" style="color: ${rec.color};"></i>
                        <span><strong>${rec.title}:</strong> ${rec.description}</span>
                    </div>
                `;
            });

            html += '</div>';

            // Ajouter un résumé en bas
            html += `
                <div style="background: #f8fafc; padding: 20px; border-radius: 12px; margin-top: 20px;">
                    <h5 style="color: #374151; margin-bottom: 15px;">📊 Résumé des actions:</h5>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                            <div style="font-size: 1.5em; color: ${nonCompliantChecks > 0 ? '#dc2626' : '#10b981'}; margin-bottom: 5px;">
                                ${nonCompliantChecks}
                            </div>
                            <div style="font-size: 0.9em; color: #64748b;">Actions critiques</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                            <div style="font-size: 1.5em; color: ${attentionChecks > 0 ? '#f59e0b' : '#10b981'}; margin-bottom: 5px;">
                                ${attentionChecks}
                            </div>
                            <div style="font-size: 0.9em; color: #64748b;">Améliorations</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                            <div style="font-size: 1.5em; color: #3b82f6; margin-bottom: 5px;">
                                ${recommendations.length}
                            </div>
                            <div style="font-size: 0.9em; color: #64748b;">Recommandations</div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        // Fermer le pop-up avec Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && currentPopup) {
                closeCurrentPopup();
            }
        });
        let currentModal = null;

        // Fonction pour créer et afficher un pop-up
        function showModal(title, content, type = 'info', actions = null) {
            // Fermer le modal existant s'il y en a un
            if (currentModal) {
                closeModal();
            }

            // Créer l'overlay du modal
            const overlay = document.createElement('div');
            overlay.className = 'modal-overlay';
            overlay.id = 'modalOverlay';

            // Créer le conteneur du modal
            const container = document.createElement('div');
            container.className = 'modal-container';

            // Header du modal
            const header = document.createElement('div');
            header.className = 'modal-header';

            // Déterminer la couleur du header selon le type
            let headerGradient = 'var(--primary-gradient)';
            if (type === 'costs') headerGradient = 'var(--secondary-gradient)';
            else if (type === 'environment') headerGradient = 'var(--warning-gradient)';
            else if (type === 'optimization') headerGradient = 'var(--danger-gradient)';

            header.style.background = headerGradient;

            header.innerHTML = `
                <h2 class="modal-title">${title}</h2>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // Body du modal
            const body = document.createElement('div');
            body.className = 'modal-body';
            body.innerHTML = content;

            // Actions du modal (optionnel)
            if (actions) {
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'modal-actions';
                actionsDiv.innerHTML = actions;
                container.appendChild(actionsDiv);
            }

            // Assembler le modal
            container.appendChild(header);
            container.appendChild(body);
            overlay.appendChild(container);

            // Ajouter au DOM
            document.body.appendChild(overlay);
            currentModal = overlay;

            // Animer l'apparition
            setTimeout(() => {
                overlay.classList.add('active');
            }, 10);

            // Fermer en cliquant sur l'overlay
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    closeModal();
                }
            });

            // Fermer avec Escape
            document.addEventListener('keydown', handleEscapeKey);
        }

        // Fonction pour fermer le modal
        function closeModal() {
            if (currentModal) {
                currentModal.classList.remove('active');
                setTimeout(() => {
                    if (currentModal && currentModal.parentNode) {
                        currentModal.parentNode.removeChild(currentModal);
                    }
                    currentModal = null;
                }, 300);
                document.removeEventListener('keydown', handleEscapeKey);
            }
        }

        // Gérer la touche Escape
        function handleEscapeKey(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        }

        // Fonction pour afficher un modal de chargement
        function showLoadingModal(title, message) {
            const loadingContent = `
                <div class="modal-loading">
                    <div class="spinner"></div>
                    <p>${message}</p>
                </div>
            `;
            showModal(title, loadingContent);
        }

        // Fonction pour mettre à jour le contenu d'un modal
        function updateModalContent(content) {
            if (currentModal) {
                const body = currentModal.querySelector('.modal-body');
                if (body) {
                    body.innerHTML = content;
                }
            }
        }

        // Fonction pour créer des onglets dans un modal
        function createModalTabs(tabs) {
            let tabsHtml = '<div class="modal-tabs">';
            let contentHtml = '';

            tabs.forEach((tab, index) => {
                const isActive = index === 0 ? 'active' : '';
                tabsHtml += `<button class="modal-tab ${isActive}" onclick="switchModalTab('${tab.id}')">${tab.title}</button>`;
                contentHtml += `<div class="modal-tab-content ${isActive}" id="${tab.id}">${tab.content}</div>`;
            });

            tabsHtml += '</div>';
            return tabsHtml + contentHtml;
        }

        // Fonction pour changer d'onglet dans un modal
        function switchModalTab(tabId) {
            // Désactiver tous les onglets
            document.querySelectorAll('.modal-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.modal-tab-content').forEach(content => content.classList.remove('active'));

            // Activer l'onglet sélectionné
            document.querySelector(`button[onclick="switchModalTab('${tabId}')"]`).classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }
    </script>
</body>
</html>
