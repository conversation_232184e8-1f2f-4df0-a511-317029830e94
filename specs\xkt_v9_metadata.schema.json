{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"projectId": {"type": "string"}, "revisionId": {"type": "string"}, "author": {"type": "string"}, "createdAt": {"type": "string"}, "schema": {"type": "string"}, "propertySets": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "properties": {"type": "array", "items": [{"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "number"}, "value": {"type": "string"}, "valueType": {"type": "number"}}, "required": ["id", "name", "type", "value"]}]}}, "required": ["id", "name", "type", "properties"]}]}, "metaObjects": {"type": "array", "items": [{"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "id": {"type": "string"}, "parent": {"type": "string"}, "propertySetIds": {"type": "array", "items": [{"type": "string"}]}}, "required": ["name", "type", "id"]}]}}, "required": ["projectId", "revisionId", "author", "createdAt", "schema", "metaObjects"]}