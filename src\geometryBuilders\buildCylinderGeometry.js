/**
 * @desc Creates cylinder-shaped geometry arrays.
 *
 * ## Usage
 *
 * In the example below we'll create an {@link XKTModel}, then create an {@link XKTMesh} with a cylinder-shaped {@link XKTGeometry}.
 *
 * [[Run this example](http://xeokit.github.io/xeokit-sdk/examples/#geometry_builders_buildCylinderGeometry)]
 *
 * ````javascript
 * const xktModel = new XKTModel();
 *
 * const cylinder = buildCylinderGeometry({
 *      center: [0,0,0],
 *      radiusTop: 2.0,
 *      radiusBottom: 2.0,
 *      height: 5.0,
 *      radialSegments: 20,
 *      heightSegments: 1,
 *      openEnded: false
 * });
 *
 * const xktGeometry = xktModel.createGeometry({
 *      geometryId: "cylinderGeometry",
 *      primitiveType: cylinder.primitiveType,
 *      positions: cylinder.positions,
 *      normals: cylinder.normals,
 *      indices: cylinder.indices
 * });
 *
 * const xktMesh = xktModel.createMesh({
 *      meshId: "redCylinderMesh",
 *      geometryId: "cylinderGeometry",
 *      position: [-4, -6, -4],
 *      scale: [1, 3, 1],
 *      rotation: [0, 0, 0],
 *      color: [1, 0, 0],
 *      opacity: 1
 * });
 *
 * const xktEntity = xktModel.createEntity({
 *      entityId: "redCylinder",
 *      meshIds: ["redCylinderMesh"]
 *  });
 *
 * xktModel.finalize();
 * ````
 *
 * @function buildCylinderGeometry
 * @param {*} [cfg] Configs
 * @param {Number[]} [cfg.center] 3D point indicating the center position.
 * @param {Number} [cfg.radiusTop=1]  Radius of top.
 * @param {Number} [cfg.radiusBottom=1]  Radius of bottom.
 * @param {Number} [cfg.height=1] Height.
 * @param {Number} [cfg.radialSegments=60]  Number of horizontal segments.
 * @param {Number} [cfg.heightSegments=1]  Number of vertical segments.
 * @param {Boolean} [cfg.openEnded=false]  Whether or not the cylinder has solid caps on the ends.
 * @returns {Object} Geometry arrays for {@link XKTModel#createGeometry} or {@link XKTModel#createMesh}.
 */
function buildCylinderGeometry(cfg = {}) {

    let radiusTop = cfg.radiusTop || 1;
    if (radiusTop < 0) {
        console.error("negative radiusTop not allowed - will invert");
        radiusTop *= -1;
    }

    let radiusBottom = cfg.radiusBottom || 1;
    if (radiusBottom < 0) {
        console.error("negative radiusBottom not allowed - will invert");
        radiusBottom *= -1;
    }

    let height = cfg.height || 1;
    if (height < 0) {
        console.error("negative height not allowed - will invert");
        height *= -1;
    }

    let radialSegments = cfg.radialSegments || 32;
    if (radialSegments < 0) {
        console.error("negative radialSegments not allowed - will invert");
        radialSegments *= -1;
    }
    if (radialSegments < 3) {
        radialSegments = 3;
    }

    let heightSegments = cfg.heightSegments || 1;
    if (heightSegments < 0) {
        console.error("negative heightSegments not allowed - will invert");
        heightSegments *= -1;
    }
    if (heightSegments < 1) {
        heightSegments = 1;
    }

    const openEnded = !!cfg.openEnded;

    let center = cfg.center;
    const centerX = center ? center[0] : 0;
    const centerY = center ? center[1] : 0;
    const centerZ = center ? center[2] : 0;

    const heightHalf = height / 2;
    const heightLength = height / heightSegments;
    const radialAngle = (2.0 * Math.PI / radialSegments);
    const radialLength = 1.0 / radialSegments;
    //var nextRadius = this._radiusBottom;
    const radiusChange = (radiusTop - radiusBottom) / heightSegments;

    const positions = [];
    const normals = [];
    const uvs = [];
    const indices = [];

    let h;
    let i;

    let x;
    let z;

    let currentRadius;
    let currentHeight;

    let first;
    let second;

    let startIndex;
    let tu;
    let tv;

    // create vertices
    const normalY = (90.0 - (Math.atan(height / (radiusBottom - radiusTop))) * 180 / Math.PI) / 90.0;

    for (h = 0; h <= heightSegments; h++) {
        currentRadius = radiusTop - h * radiusChange;
        currentHeight = heightHalf - h * heightLength;

        for (i = 0; i <= radialSegments; i++) {
            x = Math.sin(i * radialAngle);
            z = Math.cos(i * radialAngle);

            normals.push(currentRadius * x);
            normals.push(normalY); //todo
            normals.push(currentRadius * z);

            uvs.push((i * radialLength));
            uvs.push(h * 1 / heightSegments);

            positions.push((currentRadius * x) + centerX);
            positions.push((currentHeight) + centerY);
            positions.push((currentRadius * z) + centerZ);
        }
    }

    // create faces
    for (h = 0; h < heightSegments; h++) {
        for (i = 0; i <= radialSegments; i++) {

            first = h * (radialSegments + 1) + i;
            second = first + radialSegments;

            indices.push(first);
            indices.push(second);
            indices.push(second + 1);

            indices.push(first);
            indices.push(second + 1);
            indices.push(first + 1);
        }
    }

    // create top cap
    if (!openEnded && radiusTop > 0) {
        startIndex = (positions.length / 3);

        // top center
        normals.push(0.0);
        normals.push(1.0);
        normals.push(0.0);

        uvs.push(0.5);
        uvs.push(0.5);

        positions.push(0 + centerX);
        positions.push(heightHalf + centerY);
        positions.push(0 + centerZ);

        // top triangle fan
        for (i = 0; i <= radialSegments; i++) {
            x = Math.sin(i * radialAngle);
            z = Math.cos(i * radialAngle);
            tu = (0.5 * Math.sin(i * radialAngle)) + 0.5;
            tv = (0.5 * Math.cos(i * radialAngle)) + 0.5;

            normals.push(radiusTop * x);
            normals.push(1.0);
            normals.push(radiusTop * z);

            uvs.push(tu);
            uvs.push(tv);

            positions.push((radiusTop * x) + centerX);
            positions.push((heightHalf) + centerY);
            positions.push((radiusTop * z) + centerZ);
        }

        for (i = 0; i < radialSegments; i++) {
            center = startIndex;
            first = startIndex + 1 + i;

            indices.push(first);
            indices.push(first + 1);
            indices.push(center);
        }
    }

    // create bottom cap
    if (!openEnded && radiusBottom > 0) {

        startIndex = (positions.length / 3);

        // top center
        normals.push(0.0);
        normals.push(-1.0);
        normals.push(0.0);

        uvs.push(0.5);
        uvs.push(0.5);

        positions.push(0 + centerX);
        positions.push(0 - heightHalf + centerY);
        positions.push(0 + centerZ);

        // top triangle fan
        for (i = 0; i <= radialSegments; i++) {

            x = Math.sin(i * radialAngle);
            z = Math.cos(i * radialAngle);

            tu = (0.5 * Math.sin(i * radialAngle)) + 0.5;
            tv = (0.5 * Math.cos(i * radialAngle)) + 0.5;

            normals.push(radiusBottom * x);
            normals.push(-1.0);
            normals.push(radiusBottom * z);

            uvs.push(tu);
            uvs.push(tv);

            positions.push((radiusBottom * x) + centerX);
            positions.push((0 - heightHalf) + centerY);
            positions.push((radiusBottom * z) + centerZ);
        }

        for (i = 0; i < radialSegments; i++) {

            center = startIndex;
            first = startIndex + 1 + i;

            indices.push(center);
            indices.push(first + 1);
            indices.push(first);
        }
    }

    return  {
        primitiveType: "triangles",
        positions: positions,
        normals: normals,
        uv: uvs,
        uvs: uvs,
        indices: indices
    };
}


export {buildCylinderGeometry};
