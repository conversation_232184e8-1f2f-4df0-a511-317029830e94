
html, body {
    height: 100%;
    background: #f2f2f2;
    touch-action: none;
}

body {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    line-height: 1.50;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    overflow: hidden;
}

#myViewer {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: stretch;
    z-index: 10000;
    /*overflow: hidden;*/
    position: absolute;
    left: 0;
    right: 0;
    --left: 0;
    --right: 0;
    transition: all 300ms ease-in-out;
}

#myToolbar {
    min-width: 400px;
    top: 0;
    align-items: center;
    justify-content: center;
    padding: 0;
    z-index: 100000;
    pointer-events: none;
    position: fixed;
    left: 95px;
    transition: all 300ms ease-in-out;
}

#myCanvas {
    width: 100%;
    height: 100%;
    background: #f2f2f2;
    cursor: default;
    pointer-events: all;
    margin: 0;
    position: relative;
}

#myNavCubeCanvas {
    position: absolute;
    width: 200px;
    height: 200px;
    bottom: 0;
    right: 0;
    z-index: 200000;
}

#myExplorer {
    position: fixed;
    height: 100%;
    color: #fff;
    background: #03103F;
    overflow: auto;
    border-right: 2px solid #212529;
    padding: 0px;
    padding-bottom: 100px;
    padding-left: 15px;
    user-select: none;
    top: 0;
    left: -460px;
    z-index: 10;
    width: 460px;
    transition: all 300ms ease-in-out;
}

#explorer_toggle {
    display: none;
}

.explorer_toggle_label {
    position: absolute;
    top: 10px;
    left: 20px;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    flex: 1 1 auto;
    color: #03103F;
    background-color: white;
    text-align: center;
    vertical-align: middle;
    border: 2px solid #1d2453;
    padding: .375rem .75rem;
    border-radius: .25rem;
    -webkit-appearance: button;
    overflow: visible;
    margin: 0 2px 0 0;
    box-sizing: border-box;
    align-items: flex-start;
    pointer-events: all;
    z-index: 100000;
    transition: all 300ms ease-in-out;
}

.explorer_toggle_label:hover {
    cursor: pointer;
}

#explorer_toggle:checked + .explorer_toggle_label {
    left: 480px;
    color: #fff;
    background-color: #03103F;
    border-color: #03103F;
}

#explorer_toggle:checked ~ #myExplorer {
    left: 0;
}

#explorer_toggle:checked ~ #myViewer {
    left: 460px;
    --left: 460;
    width: calc(100% - (var(--left) + var(--right)));
}

#explorer_toggle:checked ~ #myToolbar {
    left: 555px;
}

#explorer_toggle:not(:checked) + .explorer_toggle_label {
    left: 20px;
    color: #fff;
    background-color: #03103F;
    border-color: #03103F;
}

#explorer_toggle:not(:checked) ~ #myExplorer {
    left: -460px;
}

#explorer_toggle:not(:checked) ~ #myViewer {
    left: 0;
    --left: 0;
    width: calc(100% - var(--right));
}

#explorer_toggle:not(:checked) ~ #myToolbar {
    left: 95px;
}

#myInspector {
    position: absolute;
    height: 100%;
    color: #fff;
    background: #03103F;
    overflow: auto;
    border-left: 2px solid #212529;
    padding: 0;
    top: 0;
    right: -360px;
    z-index: 40;
    width: 358px;
    transition: all 300ms ease-in-out;
}

#inspector_toggle {
    display: none;
}

.inspector_toggle_label {
    position: absolute;
    top: 10px;
    right: 20px;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    flex: 1 1 auto;
    color: #03103F;
    background-color: white;
    text-align: center;
    vertical-align: middle;
    border: 2px solid #1d2453;
    padding: .375rem .75rem; /* FIXME */
    border-radius: .25rem;
    -webkit-appearance: button;
    overflow: visible;
    margin: 0 2px 0 0; /* FIXME */
    box-sizing: border-box;
    align-items: flex-start;
    pointer-events: all;
    z-index: 100000;
    transition: all 300ms ease-in-out;
}

.inspector_toggle_label:hover {
    cursor: pointer;
}

#inspector_toggle:checked + .inspector_toggle_label {
    right: 380px;
    color: #fff;
    background-color: #03103F;
    border-color: #03103F;
}

#inspector_toggle:checked ~ #myViewer {
    --right: 360;
    right:360px;
    width: calc(100% - (var(--left) + var(--right)));
}

#inspector_toggle:checked ~ #myInspector {
    right: 0;
    --right: 0;
}

#inspector_toggle:not(:checked) ~ #myInspector {
    right: -380px;
    --right: -380px;
}

#inspector_toggle:not(:checked) ~ #myViewer {
    --right: 0;
    right: 0;
    width: calc(100% - var(--left));
}

