
  name: Publish

  on:
    push:
      branches:
        - main

  permissions:
    contents: write
    pull-requests: write
    issues: write

  jobs:
    # -------------------------------------
    # 1. Release job
    # -------------------------------------
    release:
      runs-on: ubuntu-latest
      steps:
        - uses: googleapis/release-please-action@v4
          id: release
          with:
            release-type: node
            config-file: release-please-config.json
            manifest-file: .release-please-manifest.json
        # The logic below handles the npm publication:
        - uses: actions/checkout@v4
            # these if statements ensure that a publication only occurs when
            # a new release is created:
          if: ${{ steps.release.outputs.release_created }}
        - uses: actions/setup-node@v4
          with:
            node-version: '20.x'
            registry-url: 'https://registry.npmjs.org'
            always-auth: true
          if: ${{ steps.release.outputs.release_created }}
        - run: npm ci
          if: ${{ steps.release.outputs.release_created }}
        - run: npm run build
          if: ${{ steps.release.outputs.release_created }}
        - run: npm publish --access public
          env:
              NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
          if: ${{ steps.release.outputs.release_created }}