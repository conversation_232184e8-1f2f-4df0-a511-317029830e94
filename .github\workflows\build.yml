
name: Build
on:
  push:
    branches:
      - main     # Build on every push to main

jobs:
  # -------------------------------------
  # 1. Build job (for pushes to main)
  # -------------------------------------
  build:
    # Only run this job when *not* publishing a tag
    if: startsWith(github.ref, 'refs/tags/') != true
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci
      - name: Install dependencies
        run: npm run build

  security:
    if: startsWith(github.ref, 'refs/tags/') != true
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/node@master
        continue-on-error: true # To make sure that SARIF upload gets called
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --sarif-file-output=snyk.sarif
      - name: Upload result to GitHub Code Scanning
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: snyk.sarif