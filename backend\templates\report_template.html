<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BIMEX - Rapport d'Analyse BIM Avancée</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border-radius: 15px;
            margin-top: 20px;
            margin-bottom: 20px;
            position: relative;
        }



        /* Header Section */
        .header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            /* Animation désactivée pour éviter les conflits avec Chart.js */
            /* animation: float 20s ease-in-out infinite; */
        }

        /* Animation d'arrière-plan désactivée pour optimiser les performances */
        /*
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        */

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* Action Buttons */
        .action-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        /* Bouton de retour à gauche */
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        /* Dashboard Grid */
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card h3 {
            color: #1f2937;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Score Indicators */
        .score-indicator {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 10px;
            border-left: 4px solid #10b981;
        }

        .score-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 4px;
            transition: width 1s ease;
        }

        /* Charts intégrés dans les sections */
        .chart-fallback {
            width: 100%;
            height: 200px;
            background: #f8fafc;
            border: 2px dashed #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 0.9rem;
            border-radius: 8px;
        }

        /* Graphiques compacts */
        canvas {
            max-width: 100%;
            height: auto;
        }

        /* 📊 STYLES POUR PDF - Fallbacks statiques */
        @media print {
            canvas {
                display: none !important;
            }
            .chart-fallback {
                display: flex !important;
                background: white;
                border: 1px solid #d1d5db;
                padding: 20px;
                text-align: center;
            }
            .chart-fallback-content {
                width: 100%;
            }
            .chart-fallback h6 {
                margin-bottom: 15px;
                color: #374151;
                font-size: 14px;
                font-weight: bold;
            }
            .chart-fallback-data {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.8;
                color: #4b5563;
            }
            .chart-fallback-bar {
                display: inline-block;
                height: 20px;
                margin: 2px;
                border-radius: 2px;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                position: relative;
                top: auto;
                right: auto;
                justify-content: center;
                margin-bottom: 20px;
            }

            .back-button {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 10px;
                display: flex;
                justify-content: center;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white;
            }

            .action-buttons {
                display: none !important;
            }

            .back-button {
                display: none !important;
            }

            .container {
                box-shadow: none;
                margin: 0;
            }

            /* Masquer les canvas et afficher les fallbacks en mode print */
            canvas {
                display: none !important;
            }

            .chart-fallback {
                display: flex !important;
            }
        }
    </style>
</head>
<body>
    <!-- Bouton de retour -->
    <div class="back-button">
        <button class="btn btn-success" onclick="goBackToAnalysis()">
            <i class="fas fa-arrow-left"></i> Retour à l'analyse
        </button>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <button class="btn btn-primary" onclick="downloadPDF()">
            <i class="fas fa-download"></i> Télécharger PDF
        </button>
        <button class="btn btn-secondary" onclick="window.print()">
            <i class="fas fa-print"></i> Imprimer
        </button>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-building"></i> BIMEX</h1>
            <p class="subtitle">Building Information Modeling Expert</p>
            <p>Analyse BIM Intelligente Powered by AI</p>
        </div>

        <!-- Report Banner -->
        <div class="card">
            <h2 style="color: #1e3a8a; text-align: center; margin-bottom: 20px;">
                🏗️ RAPPORT D'ANALYSE BIM AVANCÉE
            </h2>
            <div style="text-align: center; color: #6b7280;">
                <p><strong>Modèle:</strong> {{ filename }}</p>
                <p><strong>Date:</strong> {{ date }}</p>
                <p>Analyse Complète • Détection d'Anomalies • Conformité PMR • IA</p>
            </div>
        </div>

        <!-- Dashboard -->
        <div class="dashboard">
            <!-- Scores BIMEX -->
            <div class="card">
                <h3><i class="fas fa-chart-line" style="color: #10b981;"></i> Scores BIMEX</h3>
                
                <div class="score-indicator">
                    <div>
                        <strong>🎯 Qualité Globale</strong>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ quality_score }}%;"></div>
                        </div>
                    </div>
                    <div class="score-value" style="color: #10b981;">{{ quality_score }}%</div>
                </div>

                <div class="score-indicator" style="border-left-color: #ef4444;">
                    <div>
                        <strong>🔧 Complexité</strong>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ complexity_score }}%; background: linear-gradient(90deg, #ef4444, #dc2626);"></div>
                        </div>
                    </div>
                    <div class="score-value" style="color: #ef4444;">{{ complexity_score }}%</div>
                </div>

                <div class="score-indicator" style="border-left-color: #8b5cf6;">
                    <div>
                        <strong>⚡ Efficacité</strong>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ efficiency_score }}%; background: linear-gradient(90deg, #8b5cf6, #7c3aed);"></div>
                        </div>
                    </div>
                    <div class="score-value" style="color: #8b5cf6;">{{ efficiency_score }}%</div>
                </div>
            </div>

            <!-- IA Analysis -->
            <div class="card">
                <h3><i class="fas fa-robot" style="color: #3b82f6;"></i> Analyse IA BIMEX</h3>
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 3rem; margin-bottom: 10px;">{{ ai_emoji }}</div>
                    <div style="font-size: 2rem; font-weight: bold; color: {{ ai_color }};">NOTE: {{ ai_grade }}</div>
                    <div style="font-size: 1.2rem; color: #6b7280;">Score IA: {{ ai_score }}/100</div>
                    <div style="margin-top: 15px; padding: 15px; background: #f3f4f6; border-radius: 10px;">
                        <strong>🎯 Recommandations IA:</strong><br>
                        {{ ai_recommendations }}
                    </div>
                </div>
            </div>

            <!-- PMR Conformity -->
            <div class="card">
                <h3><i class="fas fa-wheelchair" style="color: #f59e0b;"></i> Conformité PMR</h3>
                <div style="text-align: center; padding: 20px;">
                    <div class="score-value" style="color: {{ pmr_color }};">{{ pmr_score }}%</div>
                    <div style="font-size: 1.2rem; margin: 10px 0;">{{ pmr_status }}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ pmr_score }}%; background: {{ pmr_color }};"></div>
                    </div>
                    <div style="margin-top: 10px; color: #6b7280;">
                        Analyse de {{ pmr_total_checks }} points de contrôle
                    </div>
                </div>
            </div>
        </div>

        <!-- Note: Les graphiques sont maintenant intégrés dans leurs sections respectives -->

        <!-- Résumé Exécutif -->
        <div class="card" style="border-left: 4px solid #059669;">
            <h3><i class="fas fa-clipboard-list" style="color: #059669;"></i> Résumé Exécutif</h3>
            <p style="margin-bottom: 20px;">Ce rapport présente une analyse complète du modèle BIM fourni. <strong>Caractéristiques principales:</strong></p>
            <div style="background: #f0fdf4; padding: 20px; border-radius: 10px;">
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li style="margin-bottom: 10px;">• <strong>Surface totale:</strong> {{ total_floor_area | default("16,762") }} m²</li>
                    <li style="margin-bottom: 10px;">• <strong>Nombre d'étages:</strong> {{ total_storeys | default("10") }}</li>
                    <li style="margin-bottom: 10px;">• <strong>Nombre d'espaces:</strong> {{ total_spaces | default("8") }}</li>
                    <li style="margin-bottom: 10px;">• <strong>Anomalies détectées:</strong> {{ total_anomalies | default("437") }}</li>
                    <li style="margin-bottom: 10px;">• <strong>Conformité PMR:</strong> {{ pmr_score | default("95.3") }}%</li>
                </ul>
            </div>
        </div>

        <!-- Informations du Projet -->
        <div class="card">
            <h3><i class="fas fa-info-circle" style="color: #3b82f6;"></i> Informations du Projet</h3>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Propriété</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Valeur</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Nom du projet</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">{{ project_name | default("Project Number") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Nom du bâtiment</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">{{ building_name | default("Building Name") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Description</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">-</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Site</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">Surface:{{ surface | default("932744") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Schema IFC</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">{{ schema_ifc | default("IFC2X3") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Type de bâtiment</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">{{ building_type | default("🏗️ Non classifié") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Confiance IA</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">{{ building_confidence | default("0.0") }}%</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Méthode</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">{{ classification_method | default("Standard") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Nombre total d'éléments</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">{{ total_elements | default("2,189") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Taille du fichier</strong></td><td style="padding: 12px; border: 1px solid #e5e7eb;">{{ file_size | default("42.28 MB") }}</td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 🤖 Analyse IA BIMEX -->
        {% if ai_primary_indicators %}
        <div class="card" style="border-left: 4px solid #8b5cf6;">
            <h3><i class="fas fa-brain" style="color: #8b5cf6;"></i> 🤖 Analyse IA BIMEX</h3>
            <p style="color: #6b7280; margin-bottom: 20px;">Classification intelligente • Patterns neuronaux • Analyse multi-critères</p>

            <!-- Détails d'entraînement IA -->
            {% if training_details %}
            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <h4 style="color: #4c1d95; margin-bottom: 10px;">📊 Détails d'Entraînement IA</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div>
                        <strong>🏗️ Types de bâtiments:</strong><br>
                        <span style="color: #059669;">{{ training_details.total_building_types }}</span>
                    </div>
                    <div>
                        <strong>🧠 Patterns géométriques:</strong><br>
                        <span style="color: #059669;">{{ training_details.total_patterns }}</span>
                    </div>
                    <div>
                        <strong>🔤 Mots-clés:</strong><br>
                        <span style="color: #059669;">{{ training_details.total_keywords }}</span>
                    </div>
                    <div>
                        <strong>🎯 Patterns neuronaux:</strong><br>
                        <span style="color: #059669;">{{ training_details.neural_patterns }}</span>
                    </div>
                    <div>
                        <strong>📈 Précision estimée:</strong><br>
                        <span style="color: #059669;">{{ training_details.accuracy_estimate }}</span>
                    </div>
                    <div>
                        <strong>✅ Statut:</strong><br>
                        <span style="color: #059669;">{{ training_details.training_status }}</span>
                    </div>
                </div>
                <div style="margin-top: 10px; padding: 10px; background: #ecfdf5; border-radius: 6px;">
                    <strong>🔬 Méthode:</strong> {{ training_details.training_method }}
                </div>
            </div>
            {% endif %}

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div style="background: #f8fafc; padding: 20px; border-radius: 10px;">
                    <h4 style="color: #8b5cf6; margin-bottom: 15px;">📊 Indicateurs Primaires</h4>
                    {% for key, value in ai_primary_indicators.items() %}
                    <p style="margin-bottom: 8px;"><strong>{{ key.replace('_', ' ').title() }}:</strong> {{ value }}</p>
                    {% endfor %}
                </div>

                <div style="background: #f8fafc; padding: 20px; border-radius: 10px;">
                    <h4 style="color: #8b5cf6; margin-bottom: 15px;">🎯 Facteurs de Confiance</h4>
                    {% for key, value in ai_confidence_factors.items() %}
                    <p style="margin-bottom: 8px;"><strong>{{ key.replace('_', ' ').title() }}:</strong> {{ value }}</p>
                    {% endfor %}
                </div>
            </div>

            {% if ai_neural_patterns %}
            <div style="background: #f0f9ff; padding: 20px; border-radius: 10px;">
                <h4 style="color: #0ea5e9; margin-bottom: 15px;">🧠 Patterns Neuronaux Détectés</h4>
                <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                    {% for pattern in ai_neural_patterns %}
                    <span style="background: #e0f2fe; color: #0277bd; padding: 8px 12px; border-radius: 20px; font-size: 0.9rem;">{{ pattern }}</span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- 🏢 Classification Intelligente du Bâtiment -->
        <div class="card" style="border-left: 4px solid #3b82f6;">
            <h3><i class="fas fa-building" style="color: #3b82f6;"></i> 🏢 Classification Intelligente du Bâtiment</h3>
            <p style="color: #6b7280; margin-bottom: 20px;">{{ classification_method | default("🤖 BIMEX IA Advanced") }} • Analyse multi-critères • Confiance élevée</p>

            <div style="display: grid; grid-template-columns: 1fr 300px; gap: 20px; margin-bottom: 20px;">
                <div style="background: #f8fafc; padding: 20px; border-radius: 10px;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <div style="font-size: 2.5rem; margin-bottom: 10px;">{{ building_type | default("🏗️ Bâtiment Analysé") }}</div>
                        <div style="font-size: 1.2rem; color: #3b82f6; font-weight: bold;">Confiance: {{ building_confidence | default("85.0") }}%</div>
                    </div>

                    {% if ai_primary_indicators %}
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        {% for key, value in ai_primary_indicators.items() %}
                        <div style="background: white; padding: 12px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.1rem; font-weight: bold; color: #374151;">{{ value }}</div>
                            <div style="font-size: 0.9rem; color: #6b7280;">{{ key.replace('_', ' ').title() }}</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <div style="background: white; border-radius: 10px; padding: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h5 style="text-align: center; margin-bottom: 10px; color: #3b82f6;">📊 Analyse de Confiance</h5>
                    <canvas id="classificationChart" width="250" height="200" style="display: block;"></canvas>

                    <!-- Fallback pour PDF -->
                    <div class="chart-fallback" style="display: none;" id="classificationChartFallback">
                        <div style="text-align: center;">
                            <h6 style="margin-bottom: 15px;">🏢 Classification</h6>
                            <div style="font-family: monospace; font-size: 11px; line-height: 1.6;">
                                🎯 Type: {{ building_type }}<br>
                                📊 Confiance: {{ building_confidence }}%<br>
                                🤖 Méthode: {{ classification_method }}<br>
                                {% if ai_confidence_factors %}
                                📈 Facteurs: {{ ai_confidence_factors.premier_choix }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métriques du Bâtiment -->
        <div class="card" style="border-left: 4px solid #8b5cf6;">
            <h3><i class="fas fa-ruler-combined" style="color: #8b5cf6;"></i> Métriques du Bâtiment</h3>

            <!-- Surfaces -->
            <h4 style="color: #059669; margin: 20px 0 15px 0;">Surfaces</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Type de surface</th>
                            <th style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">Valeur (m²)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Planchers</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ floor_surfaces | default("16,761.60") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Murs</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ wall_surfaces | default("66,961.01") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Fenêtres</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ window_surfaces | default("526.00") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Portes</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ door_surfaces | default("252.00") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Toitures</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ roof_surfaces | default("726.76") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Structurel</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ structural_surfaces | default("83,722.61") }}</td></tr>
                        <tr style="background: #f9fafb; font-weight: bold;"><td style="padding: 12px; border: 1px solid #e5e7eb;">Bâtiment total</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ total_floor_area | default("16,761.60") }}</td></tr>
                    </tbody>
                </table>
            </div>

            <!-- Volumes -->
            <h4 style="color: #059669; margin: 20px 0 15px 0;">Volumes</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Type de volume</th>
                            <th style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">Valeur (m³)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Espaces</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ space_volumes | default("1,852,310.97") }}</td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Structurel</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ structural_volumes | default("3,941,292.83") }}</td></tr>
                        <tr style="background: #f9fafb; font-weight: bold;"><td style="padding: 12px; border: 1px solid #e5e7eb;">Bâtiment total</td><td style="padding: 12px; text-align: right; border: 1px solid #e5e7eb;">{{ total_volumes | default("5,793,603.80") }}</td></tr>
                    </tbody>
                </table>
            </div>

            <!-- Organisation spatiale -->
            <h4 style="color: #059669; margin: 20px 0 15px 0;">Organisation spatiale</h4>
            <div style="background: #f8fafc; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <p><strong>Étages:</strong> {{ total_storeys | default("10") }} étages identifiés</p>
                <p><strong>Espaces:</strong> {{ total_spaces | default("8") }} espaces définis</p>
                <p><strong>Types d'espaces:</strong> {{ space_types | default("1") }} types différents</p>
            </div>

            <!-- Métriques Avancées -->
            <h4 style="color: #059669; margin: 20px 0 15px 0;">Métriques Avancées</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Indicateur</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Valeur</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Évaluation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Ratio Fenêtres/Murs</td><td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ window_wall_ratio | default("0.8%") }}</td><td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;"><span style="color: #f59e0b;">À optimiser</span></td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Efficacité Spatiale</td><td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ spatial_efficiency | default("2095.2") }} m²/espace</td><td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;"><span style="color: #10b981;">Bonne</span></td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Compacité du Bâtiment</td><td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ building_compactness | default("0.20") }}</td><td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;"><span style="color: #6b7280;">Étalée</span></td></tr>
                        <tr><td style="padding: 12px; border: 1px solid #e5e7eb;">Densité d'Espaces</td><td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ space_density | default("0.8") }} espaces/étage</td><td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;"><span style="color: #ef4444;">Déséquilibrée</span></td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Analyse Intelligente des Anomalies -->
        <div class="card" style="border-left: 4px solid #dc2626;">
            <h3><i class="fas fa-exclamation-triangle" style="color: #dc2626;"></i> Analyse Intelligente des Anomalies</h3>
            <p style="color: #6b7280; margin-bottom: 20px;">Détection IA • Classification Automatique • Solutions Recommandées</p>

            <!-- Graphique principal des anomalies intégré -->
            <div style="display: grid; grid-template-columns: 1fr 300px; gap: 20px; margin-bottom: 20px;">
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f3f4f6;">
                                <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Sévérité</th>
                                <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Nombre</th>
                                <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Pourcentage</th>
                                <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Impact BIMEX</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background: #fef2f2;">
                                <td style="padding: 12px; border: 1px solid #e5e7eb;"><span style="color: #dc2626;">🔴 CRITIQUE</span></td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ critical_anomalies | default("0") }}</td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ critical_percentage | default("0%") }}</td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;"><span style="color: #dc2626;">🔴 CRITIQUE</span></td>
                            </tr>
                            <tr style="background: #fef3c7;">
                                <td style="padding: 12px; border: 1px solid #e5e7eb;"><span style="color: #f59e0b;">🟡 ÉLEVÉE</span></td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ high_anomalies | default("8") }}</td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ high_percentage | default("1.8%") }}</td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;"><span style="color: #f59e0b;">🟡 IMPORTANT</span></td>
                            </tr>
                            <tr style="background: #fef9c3;">
                                <td style="padding: 12px; border: 1px solid #e5e7eb;"><span style="color: #eab308;">🟨 MOYENNE</span></td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ medium_anomalies | default("17") }}</td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ medium_percentage | default("3.9%") }}</td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;"><span style="color: #eab308;">🟨 MODÉRÉ</span></td>
                            </tr>
                            <tr style="background: #f0fdf4;">
                                <td style="padding: 12px; border: 1px solid #e5e7eb;"><span style="color: #22c55e;">🟢 FAIBLE</span></td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ low_anomalies | default("412") }}</td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ low_percentage | default("94.3%") }}</td>
                                <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;"><span style="color: #22c55e;">🟢 MINEUR</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: white; border-radius: 10px; padding: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h5 style="text-align: center; margin-bottom: 10px; color: #dc2626;">📊 Répartition des Anomalies</h5>
                    <canvas id="anomaliesChart" width="250" height="200" style="display: block;"></canvas>

                    <!-- Fallback pour PDF -->
                    <div class="chart-fallback" style="display: none;" id="anomaliesChartFallback">
                        <div style="text-align: center;">
                            <h6 style="margin-bottom: 15px;">📊 Anomalies</h6>
                            <div style="font-family: monospace; font-size: 11px; line-height: 1.6;">
                                🔴 Critique: {{ critical_anomalies }} ({{ critical_percentage }}%)<br>
                                🟡 Élevée: {{ high_anomalies }} ({{ high_percentage }}%)<br>
                                🟨 Moyenne: {{ medium_anomalies }} ({{ medium_percentage }}%)<br>
                                🟢 Faible: {{ low_anomalies }} ({{ low_percentage }}%)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques Avancées BIMEX -->
        <div class="card">
            <h3><i class="fas fa-chart-bar" style="color: #8b5cf6;"></i> Statistiques Avancées BIMEX</h3>

            <!-- Graphique Visualisation Intelligente intégré -->
            <div style="display: grid; grid-template-columns: 1fr 300px; gap: 20px; margin-bottom: 20px;">
                <div style="background: #f8fafc; padding: 20px; border-radius: 10px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px;">
                        <div style="text-align: center;">
                            <div style="font-size: 1.3rem; color: #dc2626; font-weight: bold;">{{ priority_anomalies | default("8") }} ({{ priority_percentage | default("1.8") }}%)</div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Anomalies Prioritaires</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.3rem; color: #8b5cf6; font-weight: bold;">{{ criticality_index | default("1.1") }}/4.0</div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Index de Criticité BIMEX</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.1rem; color: #f59e0b; font-weight: bold;">{{ urgency | default("URGENT (1 semaine)") }}</div>
                            <div style="color: #6b7280; font-size: 0.9rem;">Délai Recommandé</div>
                        </div>
                    </div>
                    <p style="margin-top: 15px; color: #6b7280; font-size: 0.8rem; text-align: center;">Calculs basés sur l'algorithme propriétaire BIMEX</p>
                </div>

                <div style="background: white; border-radius: 10px; padding: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h5 style="text-align: center; margin-bottom: 10px; color: #8b5cf6;">📊 Visualisation Intelligente</h5>
                    <canvas id="bimexChart" width="250" height="180" style="display: block;"></canvas>

                    <!-- Fallback pour PDF -->
                    <div class="chart-fallback" style="display: none;" id="bimexChartFallback">
                        <div style="text-align: center;">
                            <h6 style="margin-bottom: 15px;">📊 Répartition BIMEX</h6>
                            <div style="font-family: monospace; font-size: 11px; line-height: 1.5;">
                                🔴 Critique: {{ critical_anomalies }}<br>
                                🟡 Élevée: {{ high_anomalies }}<br>
                                🟨 Moyenne: {{ medium_anomalies }}<br>
                                🟢 Faible: {{ low_anomalies }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Problèmes les plus fréquents -->
        <div class="card">
            <h3><i class="fas fa-list" style="color: #ef4444;"></i> Problèmes les plus fréquents</h3>
            <div style="space-y: 15px;">
                {% for problem in frequent_problems %}
                <div style="padding: 15px; background: #fef2f2; border-left: 4px solid #ef4444; margin-bottom: 15px;">
                    <h4 style="color: #dc2626; margin-bottom: 10px;">{{ loop.index }}. {{ problem }}</h4>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Anomalies prioritaires à corriger -->
        <div class="card" style="border-left: 4px solid #dc2626;">
            <h3><i class="fas fa-tools" style="color: #dc2626;"></i> Anomalies prioritaires à corriger</h3>

            {% if priority_anomalies_list %}
                {% for anomaly in priority_anomalies_list %}
                <div style="background: #fef2f2; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h4 style="color: #dc2626; margin-bottom: 15px;">🔧 {{ anomaly.type }} ({{ anomaly.count }} élément(s))</h4>
                    <p style="margin-bottom: 10px;"><strong>Problème:</strong> {{ anomaly.description }}</p>
                    <p style="margin-bottom: 15px;"><strong>Solution suggérée:</strong> {{ anomaly.solution }}</p>
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <p style="margin-bottom: 10px;"><strong>Éléments concernés ({{ anomaly.count }}):</strong></p>
                        <div style="font-family: monospace; font-size: 0.9rem; color: #374151;">
                            {% for element in anomaly.elements %}
                            • {{ element }}{% if not loop.last %}<br>{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
            <div style="background: #f0fdf4; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h4 style="color: #059669; margin-bottom: 15px;">✅ Aucune anomalie prioritaire détectée</h4>
                <p style="margin-bottom: 10px;"><strong>Félicitations !</strong> Votre modèle BIM ne présente aucune anomalie nécessitant une correction immédiate.</p>
                <p><strong>Recommandation:</strong> Continuez à maintenir cette qualité lors des futures modifications.</p>
            </div>
            {% endif %}
        </div>

        <!-- Analyse d'Accessibilité PMR Détaillée -->
        <div class="card" style="border-left: 4px solid #f59e0b;">
            <h3><i class="fas fa-wheelchair" style="color: #f59e0b;"></i> Analyse d'Accessibilité PMR</h3>
            <div style="background: #fef2f2; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                <div style="text-align: center; font-size: 1.2rem; color: #dc2626; font-weight: bold;">
                    🔴 NON CONFORME - Score: {{ pmr_score }}%
                </div>
                <p style="text-align: center; color: #6b7280; margin-top: 10px;">
                    Basé sur {{ pmr_total_checks }} vérifications d'accessibilité selon les normes françaises
                </p>
            </div>

            <!-- Graphique PMR intégré -->
            <div style="display: grid; grid-template-columns: 1fr 300px; gap: 20px; margin-bottom: 20px;">
                <div>
                    <h4 style="color: #f59e0b; margin-bottom: 15px;">📊 Répartition PMR</h4>
                </div>
                <div style="background: white; border-radius: 10px; padding: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <canvas id="pmrChart" width="250" height="200" style="display: block;"></canvas>

                    <!-- Fallback pour PDF -->
                    <div class="chart-fallback" style="display: none;" id="pmrChartFallback">
                        <div style="text-align: center;">
                            <h5 style="margin-bottom: 15px;">♿ Conformité PMR</h5>
                            <div style="font-family: monospace; font-size: 12px; line-height: 1.6;">
                                🟢 Conforme: {{ pmr_conforme | default("143") }} ({{ pmr_conforme_percentage | default("95.3") }}%)<br>
                                🔴 Non conforme: {{ pmr_non_conforme | default("1") }} ({{ pmr_non_conforme_percentage | default("0.7") }}%)<br>
                                🟡 Attention: {{ pmr_attention | default("5") }} ({{ pmr_attention_percentage | default("3.3") }}%)<br>
                                ⚪ Non applicable: {{ pmr_non_applicable | default("1") }} ({{ pmr_non_applicable_percentage | default("0.7") }}%)
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tableau détaillé PMR -->
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Statut</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Nombre</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Pourcentage</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Indicateur</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;"><span style="color: #10b981;">🟢 Conforme</span></td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ pmr_conforme | default("143") }}</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ pmr_conforme_percentage | default("95.3") }}%</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb; font-family: monospace;"><span style="color: #10b981;">{{ pmr_conforme_bar | default("██████████") }}</span></td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;"><span style="color: #ef4444;">🔴 Non conforme</span></td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ pmr_non_conforme | default("1") }}</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ pmr_non_conforme_percentage | default("0.7") }}%</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb; font-family: monospace;"><span style="color: #ef4444;">{{ pmr_non_conforme_bar | default("█") }}</span></td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;"><span style="color: #f59e0b;">🟡 Attention</span></td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ pmr_attention | default("5") }}</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ pmr_attention_percentage | default("3.3") }}%</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb; font-family: monospace;"><span style="color: #f59e0b;">{{ pmr_attention_bar | default("████") }}</span></td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;"><span style="color: #6b7280;">⚪ Non applicable</span></td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ pmr_non_applicable | default("1") }}</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ pmr_non_applicable_percentage | default("0.7") }}%</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb; font-family: monospace;"><span style="color: #6b7280;">{{ pmr_non_applicable_bar | default("█") }}</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Non-conformités à corriger -->
            <div style="background: #fef2f2; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h4 style="color: #dc2626; margin-bottom: 15px;">🚨 Non-conformités à corriger</h4>
                {% if pmr_non_conformities %}
                    {% for non_conformity in pmr_non_conformities %}
                    <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                        <p><strong>{{ loop.index }}. {{ non_conformity.category }}</strong></p>
                        <p style="color: #6b7280;">{{ non_conformity.description }}</p>
                        <p style="color: #059669;"><strong>Recommandation:</strong> {{ non_conformity.recommendation }}</p>
                        <p style="color: #6b7280; font-size: 0.9rem;"><strong>Référence:</strong> {{ non_conformity.reference }}</p>
                    </div>
                    {% endfor %}
                {% else %}
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <p><strong>1. Bâtiment</strong></p>
                    <p style="color: #6b7280;">Vérification présence ascenseur ({{ total_storeys }} étages, 0 ascenseur(s))</p>
                    <p style="color: #059669;"><strong>Recommandation:</strong> Installer un ascenseur pour l'accessibilité PMR</p>
                    <p style="color: #6b7280; font-size: 0.9rem;"><strong>Référence:</strong> Article R111-19-4 du CCH</p>
                </div>
                {% endif %}
            </div>

            <!-- Recommandations PMR -->
            <div style="background: #f0fdf4; padding: 20px; border-radius: 10px;">
                <h4 style="color: #059669; margin-bottom: 15px;">💡 Recommandations PMR</h4>
                <div style="space-y: 10px;">
                    {% for recommendation in pmr_recommendations %}
                    <div style="padding: 10px; background: white; border-radius: 8px; margin-bottom: 10px;">
                        <strong>{{ loop.index }}.</strong> {{ recommendation }}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 🚀 CORRECTION: Recommandations Dynamiques -->
        <div class="card" style="border-left: 4px solid #f59e0b;">
            <h3><i class="fas fa-lightbulb" style="color: #f59e0b;"></i> Recommandations Intelligentes</h3>
            <div style="space-y: 15px;">
                {% if recommendations %}
                    {% for recommendation in recommendations %}
                    <div style="padding: 15px; background: {% if 'Priorité 1' in recommendation or 'critiques' in recommendation %}#fef2f2{% elif 'Priorité 2' in recommendation or 'élevée' in recommendation %}#fef3c7{% elif 'PMR' in recommendation %}#fef2f2{% else %}#f0fdf4{% endif %}; border-left: 4px solid {% if 'Priorité 1' in recommendation or 'critiques' in recommendation %}#ef4444{% elif 'Priorité 2' in recommendation or 'élevée' in recommendation %}#f59e0b{% elif 'PMR' in recommendation %}#ef4444{% else %}#10b981{% endif %}; margin-bottom: 15px;">
                        <p><strong>{{ loop.index }}.</strong> {{ recommendation | safe }}</p>
                    </div>
                    {% endfor %}
                {% else %}
                    <!-- Fallback si pas de recommandations dynamiques -->
                    <div style="padding: 15px; background: #f0fdf4; border-left: 4px solid #10b981; margin-bottom: 15px;">
                        <p><strong>✅ Modèle de qualité:</strong> Aucune recommandation critique identifiée.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Plan d'Action Intelligent BIMEX -->
        <div class="card" style="border-left: 4px solid #3b82f6;">
            <h3><i class="fas fa-road" style="color: #3b82f6;"></i> Plan d'Action Intelligent BIMEX</h3>
            <p style="color: #6b7280; margin-bottom: 20px;">Roadmap Personnalisée • Priorités IA • Timeline Optimisée</p>

            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #eff6ff;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Échéance</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Action</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Responsable</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Court terme (1-4 semaines)</strong></td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">
                                {% if high_anomalies > 0 %}
                                    Traiter {{ high_anomalies }} anomalies élevées
                                {% else %}
                                    Maintenir la qualité BIM actuelle
                                {% endif %}
                            </td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">Équipe BIM</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Moyen terme (1-3 mois)</strong></td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">
                                {% if pmr_score < 95 %}
                                    Corriger {{ pmr_non_conformities|length }} non-conformités PMR
                                {% else %}
                                    Maintenir la conformité PMR
                                {% endif %}
                            </td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">Architecte + BIM</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>Long terme (3-6 mois)</strong></td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">
                                {% if total_anomalies > 100 %}
                                    Améliorer processus qualité BIM
                                {% else %}
                                    Optimiser les performances du modèle
                                {% endif %}
                            </td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">Management</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 🔮 NOUVELLES SECTIONS DATA SCIENCE -->

        <!-- Section Prédiction des Coûts IA -->
        {% if cost_prediction %}
        <div style="page-break-before: always;"></div>
        <div class="card" style="border-left: 4px solid #f093fb;">
            <h3><i class="fas fa-coins" style="color: #f093fb;"></i> 🔮 Prédiction Intelligente des Coûts</h3>
            <p style="color: #6b7280; margin-bottom: 20px;">Analyse IA • Machine Learning • Prédictions Avancées</p>

            <!-- Métriques principales -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em;">💰 Coût Total Prédit</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px;">{{ "{:,.0f}".format(cost_prediction.total_predicted_cost) }} €</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">Confiance: {{ "{:.0f}".format(cost_prediction.confidence_score * 100) }}%</div>
                </div>

                <div style="background: #ffffff; border: 2px solid #f093fb; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em; color: #f093fb;">📐 Coût par m²</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px; color: #333;">{{ "{:.0f}".format(cost_prediction.cost_per_m2) }} €/m²</div>
                    <div style="font-size: 0.9em; color: #666;">Estimation IA</div>
                </div>
            </div>

            <!-- Répartition des coûts -->
            <h4 style="color: #f093fb; margin-bottom: 15px;">📊 Répartition des Coûts par Catégorie</h4>
            <div style="overflow-x: auto; margin-bottom: 20px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #fdf2f8;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Catégorie</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Coût (€)</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Pourcentage</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Visualisation</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category, cost_data in cost_prediction.cost_breakdown.items() %}
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb; text-transform: capitalize;">{{ category }}</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ "{:,.0f}".format(cost_data.total_cost) }}</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ "{:.1f}".format((cost_data.total_cost / cost_prediction.total_predicted_cost) * 100) }}%</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">
                                <div style="background: #f3f4f6; height: 8px; border-radius: 4px; overflow: hidden;">
                                    <div style="background: linear-gradient(90deg, #f093fb, #f5576c); height: 100%; width: {{ "{:.1f}".format((cost_data.total_cost / cost_prediction.total_predicted_cost) * 100) }}%;"></div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Recommandations d'optimisation des coûts -->
            <div style="background: #fef7ff; padding: 20px; border-radius: 10px;">
                <h4 style="color: #a855f7; margin-bottom: 15px;">💡 Recommandations d'Optimisation des Coûts</h4>
                {% for recommendation in cost_prediction.optimization_recommendations %}
                <div style="padding: 10px; background: white; border-left: 4px solid #f093fb; margin-bottom: 10px;">
                    <strong>{{ loop.index }}.</strong> {{ recommendation }}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Section Analyse Environnementale -->
        {% if environmental_analysis %}
        <div style="page-break-before: always;"></div>
        <div class="card" style="border-left: 4px solid #43e97b;">
            <h3><i class="fas fa-leaf" style="color: #43e97b;"></i> 🌱 Analyse Environnementale & Durabilité</h3>
            <p style="color: #6b7280; margin-bottom: 20px;">Empreinte Carbone • Durabilité • Optimisations Écologiques</p>

            <!-- Métriques environnementales principales -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em;">🌍 Empreinte Carbone</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px;">{{ "{:,.0f}".format(environmental_analysis.total_co2_emissions) }} kg</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">CO₂ équivalent</div>
                </div>

                <div style="background: #ffffff; border: 2px solid #43e97b; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em; color: #43e97b;">⭐ Score Durabilité</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px; color: #333;">{{ "{:.1f}".format(environmental_analysis.sustainability_score) }}/10</div>
                    <div style="font-size: 0.9em; color: #666;">Classe {{ environmental_analysis.environmental_rating }}</div>
                </div>

                <div style="background: #ffffff; border: 2px solid #38f9d7; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em; color: #38f9d7;">♻️ Recyclabilité</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px; color: #333;">{{ "{:.0f}".format(environmental_analysis.recyclability_analysis.average_recyclability_score * 10) }}%</div>
                    <div style="font-size: 0.9em; color: #666;">Potentiel recyclage</div>
                </div>
            </div>

            <!-- Analyse énergétique -->
            <h4 style="color: #43e97b; margin-bottom: 15px;">⚡ Performance Énergétique</h4>
            <div style="background: #f0fdf4; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #059669;">{{ "{:,.0f}".format(environmental_analysis.energy_analysis.total_energy_demand) }}</div>
                        <div style="font-size: 0.9em; color: #666;">kWh/an total</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #059669;">{{ "{:.0f}".format(environmental_analysis.energy_analysis.energy_intensity) }}</div>
                        <div style="font-size: 0.9em; color: #666;">kWh/m²/an</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #059669;">{{ environmental_analysis.energy_analysis.energy_class }}</div>
                        <div style="font-size: 0.9em; color: #666;">Classe énergétique</div>
                    </div>
                </div>
            </div>

            <!-- Recommandations environnementales -->
            <div style="background: #ecfdf5; padding: 20px; border-radius: 10px;">
                <h4 style="color: #059669; margin-bottom: 15px;">🌱 Recommandations Environnementales</h4>
                {% for recommendation in environmental_analysis.optimization_recommendations %}
                <div style="padding: 15px; background: white; border-left: 4px solid #43e97b; margin-bottom: 15px; border-radius: 0 8px 8px 0;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 8px;">
                        <strong style="color: #059669;">{{ recommendation.category }}</strong>
                        <span style="background: {% if recommendation.implementation_difficulty == 'Easy' %}#d1fae5{% elif recommendation.implementation_difficulty == 'Medium' %}#fef3c7{% else %}#fecaca{% endif %}; color: {% if recommendation.implementation_difficulty == 'Easy' %}#065f46{% elif recommendation.implementation_difficulty == 'Medium' %}#92400e{% else %}#991b1b{% endif %}; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">{{ recommendation.implementation_difficulty }}</span>
                    </div>
                    <p style="margin: 8px 0;">{{ recommendation.recommendation }}</p>
                    <div style="display: flex; gap: 20px; font-size: 0.85em; color: #666; margin-top: 8px;">
                        <span><i class="fas fa-leaf"></i> -{{ "{:.0f}".format(recommendation.potential_co2_reduction) }} kg CO₂/an</span>
                        <span><i class="fas fa-clock"></i> {{ "{:.1f}".format(recommendation.payback_period) }} ans</span>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Section Optimisation IA -->
        {% if ai_optimization %}
        <div style="page-break-before: always;"></div>
        <div class="card" style="border-left: 4px solid #fa709a;">
            <h3><i class="fas fa-magic" style="color: #fa709a;"></i> ⚡ Optimisation Automatique avec IA</h3>
            <p style="color: #6b7280; margin-bottom: 20px;">Intelligence Artificielle • Recommandations Avancées • Optimisations Structurelles</p>

            <!-- Métriques d'optimisation principales -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em;">🎯 Recommandations IA</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px;">{{ ai_optimization.total_recommendations }}</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">Optimisations identifiées</div>
                </div>

                <div style="background: #ffffff; border: 2px solid #fa709a; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em; color: #fa709a;">💰 Économies Potentielles</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px; color: #333;">{{ "{:,.0f}".format(ai_optimization.cost_benefit_analysis.total_annual_savings) }} €</div>
                    <div style="font-size: 0.9em; color: #666;">Par an</div>
                </div>

                <div style="background: #ffffff; border: 2px solid #fee140; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em; color: #d97706;">⚡ Économies Énergétiques</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px; color: #333;">{{ "{:,.0f}".format(ai_optimization.energy_optimization.total_energy_savings) }}</div>
                    <div style="font-size: 0.9em; color: #666;">kWh/an</div>
                </div>

                <div style="background: #ffffff; border: 2px solid #10b981; padding: 20px; border-radius: 12px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0; font-size: 1.1em; color: #10b981;">⏱️ ROI</h4>
                    <div style="font-size: 2em; font-weight: bold; margin-bottom: 5px; color: #333;">{{ "{:.1f}".format(ai_optimization.cost_benefit_analysis.payback_period) }}</div>
                    <div style="font-size: 0.9em; color: #666;">ans</div>
                </div>
            </div>

            <!-- Optimisations par catégorie -->
            <h4 style="color: #fa709a; margin-bottom: 15px;">🏗️ Optimisations par Catégorie</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                {% if ai_optimization.structural_optimization %}
                <div style="background: #fef7ff; border: 1px solid #e879f9; padding: 20px; border-radius: 12px;">
                    <h5 style="color: #a855f7; margin-bottom: 10px;"><i class="fas fa-building"></i> Optimisation Structurelle</h5>
                    <p style="margin: 5px 0; font-size: 0.9em;">Économies matériaux: <strong>{{ "{:,.0f}".format(ai_optimization.structural_optimization.total_material_savings) }} €</strong></p>
                    <p style="margin: 5px 0; font-size: 0.9em;">Potentiel: <strong>{{ ai_optimization.structural_optimization.optimization_potential }}</strong></p>
                </div>
                {% endif %}

                {% if ai_optimization.lighting_optimization %}
                <div style="background: #fffbeb; border: 1px solid #fbbf24; padding: 20px; border-radius: 12px;">
                    <h5 style="color: #d97706; margin-bottom: 10px;"><i class="fas fa-lightbulb"></i> Éclairage Naturel</h5>
                    <p style="margin: 5px 0; font-size: 0.9em;">Ratio optimal: <strong>{{ "{:.0f}".format(ai_optimization.lighting_optimization.optimal_window_ratio * 100) }}%</strong></p>
                    <p style="margin: 5px 0; font-size: 0.9em;">Amélioration: <strong>{{ "{:.0f}".format(ai_optimization.lighting_optimization.daylight_factor_improvement * 100) }}%</strong></p>
                </div>
                {% endif %}

                {% if ai_optimization.thermal_optimization %}
                <div style="background: #f0fdf4; border: 1px solid #22c55e; padding: 20px; border-radius: 12px;">
                    <h5 style="color: #16a34a; margin-bottom: 10px;"><i class="fas fa-thermometer-half"></i> Performance Thermique</h5>
                    <p style="margin: 5px 0; font-size: 0.9em;">Améliorations: <strong>{{ ai_optimization.thermal_optimization.thermal_improvements|length }}</strong></p>
                    <p style="margin: 5px 0; font-size: 0.9em;">Gain global: <strong>{{ "{:.0f}".format(ai_optimization.thermal_optimization.overall_thermal_improvement * 100) }}%</strong></p>
                </div>
                {% endif %}

                {% if ai_optimization.energy_optimization %}
                <div style="background: #eff6ff; border: 1px solid #3b82f6; padding: 20px; border-radius: 12px;">
                    <h5 style="color: #2563eb; margin-bottom: 10px;"><i class="fas fa-plug"></i> Systèmes Énergétiques</h5>
                    <p style="margin: 5px 0; font-size: 0.9em;">Économies: <strong>{{ "{:,.0f}".format(ai_optimization.energy_optimization.total_cost_savings) }} €</strong></p>
                    <p style="margin: 5px 0; font-size: 0.9em;">Renouvelable: <strong>{{ "{:,.0f}".format(ai_optimization.energy_optimization.renewable_integration.total_renewable_capacity) }} kWh</strong></p>
                </div>
                {% endif %}
            </div>

            <!-- Feuille de route d'implémentation -->
            <h4 style="color: #fa709a; margin-bottom: 15px;">🗺️ Feuille de Route d'Implémentation</h4>
            <div style="background: #fef7ff; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                {% for phase in ai_optimization.implementation_roadmap %}
                <div style="background: white; border-left: 4px solid #fa709a; padding: 15px; margin-bottom: 15px; border-radius: 0 8px 8px 0;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                        <h5 style="margin: 0; color: #a855f7;">{{ phase.phase }}</h5>
                        <span style="background: #fa709a; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.85em;">{{ phase.duration }}</span>
                    </div>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        {% for recommendation in phase.recommendations %}
                        <li style="margin: 5px 0; color: #666;">{{ recommendation }}</li>
                        {% endfor %}
                    </ul>
                    <div style="text-align: right; font-weight: bold; color: #a855f7;">
                        Coût estimé: {{ "{:,.0f}".format(phase.total_cost) }} €
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Recommandations prioritaires -->
            <div style="background: #fef2f2; padding: 20px; border-radius: 10px;">
                <h4 style="color: #dc2626; margin-bottom: 15px;">⭐ Recommandations Prioritaires</h4>
                {% for recommendation in ai_optimization.prioritized_recommendations[:5] %}
                <div style="display: flex; align-items: flex-start; gap: 15px; background: white; padding: 15px; margin-bottom: 15px; border-radius: 8px; border-left: 4px solid {% if recommendation.priority == 'High' %}#dc2626{% elif recommendation.priority == 'Medium' %}#f59e0b{% else %}#10b981{% endif %};">
                    <div style="background: {% if recommendation.priority == 'High' %}#dc2626{% elif recommendation.priority == 'Medium' %}#f59e0b{% else %}#10b981{% endif %}; color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; flex-shrink: 0;">{{ loop.index }}</div>
                    <div style="flex: 1;">
                        <h6 style="margin: 0 0 8px 0; color: #333;">{{ recommendation.recommendation }}</h6>
                        <div style="display: flex; gap: 15px; margin-bottom: 8px; font-size: 0.85em;">
                            <span style="background: #dbeafe; color: #1e40af; padding: 2px 8px; border-radius: 12px;">Impact: {{ "{:.1f}".format(recommendation.impact_score) }}/10</span>
                            <span style="background: {% if recommendation.difficulty == 'Easy' %}#d1fae5{% elif recommendation.difficulty == 'Medium' %}#fef3c7{% else %}#fecaca{% endif %}; color: {% if recommendation.difficulty == 'Easy' %}#065f46{% elif recommendation.difficulty == 'Medium' %}#92400e{% else %}#991b1b{% endif %}; padding: 2px 8px; border-radius: 12px;">{{ recommendation.difficulty }}</span>
                            <span style="background: {% if recommendation.priority == 'High' %}#fecaca{% elif recommendation.priority == 'Medium' %}#fef3c7{% else %}#d1fae5{% endif %}; color: {% if recommendation.priority == 'High' %}#991b1b{% elif recommendation.priority == 'Medium' %}#92400e{% else %}#065f46{% endif %}; padding: 2px 8px; border-radius: 12px;">{{ recommendation.priority }}</span>
                        </div>
                        <div style="display: flex; gap: 15px; font-size: 0.85em; color: #666;">
                            <span><i class="fas fa-bolt"></i> {{ "{:,.0f}".format(recommendation.energy_savings) }} kWh/an</span>
                            <span><i class="fas fa-leaf"></i> -{{ "{:.0f}".format(recommendation.co2_reduction) }} kg CO₂/an</span>
                            <span><i class="fas fa-clock"></i> {{ "{:.1f}".format(recommendation.payback_period) }} ans</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- ANNEXES TECHNIQUES BIMEX -->
        <div style="page-break-before: always;"></div>
        <div class="card" style="border-left: 4px solid #8b5cf6;">
            <h3><i class="fas fa-file-alt" style="color: #8b5cf6;"></i> ANNEXES TECHNIQUES BIMEX</h3>
            <p style="color: #6b7280; margin-bottom: 20px;">Documentation Complète • Références • Données Détaillées</p>
        </div>

        <!-- Annexe A: Détail des espaces -->
        <div class="card">
            <h4 style="color: #059669; margin-bottom: 20px;">Annexe A: Détail des espaces</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Nom</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Type</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Surface (m²)</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">Volume (m³)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for space in space_details_list %}
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">{{ space.name }}</td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">{{ space.type }}</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ "%.1f"|format(space.area) }}</td>
                            <td style="padding: 12px; text-align: center; border: 1px solid #e5e7eb;">{{ "%.1f"|format(space.volume) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Annexe B: Références Réglementaires -->
        <div class="card">
            <h4 style="color: #059669; margin-bottom: 20px;">Annexe B: Références Réglementaires</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Domaine</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Référence</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ref in dynamic_references %}
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">{{ ref.domaine }}</td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">{{ ref.reference }}</td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">{{ ref.description }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Annexe C: Résumé des éléments structurels -->
        <div class="card">
            <h4 style="color: #059669; margin-bottom: 20px;">Annexe C: Résumé des éléments structurels</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div style="background: #f8fafc; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; color: #374151; font-weight: bold;">{{ beams_count | default("0") }}</div>
                    <div style="color: #6b7280;">Poutres</div>
                </div>
                <div style="background: #f8fafc; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; color: #374151; font-weight: bold;">{{ columns_count | default("0") }}</div>
                    <div style="color: #6b7280;">Colonnes</div>
                </div>
                <div style="background: #f8fafc; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; color: #374151; font-weight: bold;">{{ walls_count | default("390") }}</div>
                    <div style="color: #6b7280;">Murs</div>
                </div>
                <div style="background: #f8fafc; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; color: #374151; font-weight: bold;">{{ slabs_count | default("43") }}</div>
                    <div style="color: #6b7280;">Dalles</div>
                </div>
                <div style="background: #f8fafc; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; color: #374151; font-weight: bold;">{{ foundations_count | default("0") }}</div>
                    <div style="color: #6b7280;">Fondations</div>
                </div>
            </div>
        </div>

        <!-- Annexe D: Glossaire des Termes Techniques -->
        <div class="card">
            <h4 style="color: #059669; margin-bottom: 20px;">Annexe D: Glossaire des Termes Techniques</h4>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Terme</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #e5e7eb;">Définition</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for term in dynamic_glossary %}
                        <tr>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;"><strong>{{ term.terme }}</strong></td>
                            <td style="padding: 12px; border: 1px solid #e5e7eb;">{{ term.definition }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Footer -->
        <div style="margin-top: 40px; padding: 20px; background: #1e3a8a; color: white; border-radius: 10px; text-align: center;">
            <h3>🏗️ BIMEX - Building Information Modeling Expert</h3>
            <p>Rapport généré par l'IA BIMEX le {{ date }}</p>
            <p style="font-size: 0.9rem; opacity: 0.8;">Analyse Intelligente • Machine Learning • Conformité Automatisée</p>
            <div style="margin-top: 15px;">
                <span style="background: #3b82f6; padding: 5px 15px; border-radius: 20px; margin: 0 5px;">🔍 Analyse par l'Intelligence Artificielle BIMEX</span>
            </div>
            <p style="margin-top: 15px; font-size: 0.8rem; opacity: 0.7;">Précision • Rapidité • Innovation</p>
        </div>
    </div>

    <script>
        // 📊 DONNÉES RÉELLES des graphiques (injectées depuis Python)
        const anomaliesData = {{ anomalies_chart_data | safe }};
        const pmrData = {{ pmr_chart_data | safe }};

        // 🔍 Debug des données
        console.log("🔍 Données anomalies:", anomaliesData);
        console.log("🔍 Données PMR:", pmrData);

        // Vérification que les canvas existent
        const anomaliesCanvas = document.getElementById('anomaliesChart');
        const pmrCanvas = document.getElementById('pmrChart');
        const bimexCanvas = document.getElementById('bimexChart');

        console.log("🎯 Canvas trouvés:", {
            anomalies: !!anomaliesCanvas,
            pmr: !!pmrCanvas,
            bimex: !!bimexCanvas
        });

        // 🚀 CHARGEMENT SÉQUENTIEL DES GRAPHIQUES POUR ÉVITER LES VIOLATIONS
        function createChartsSequentially() {
            console.log("🎯 Début du chargement séquentiel des graphiques...");

            // Graphique 1: Anomalies (priorité haute)
            setTimeout(() => createAnomaliesChart(), 100);

            // Graphique 2: PMR (après 200ms)
            setTimeout(() => createPMRChart(), 300);

            // Graphique 3: BIMEX (après 400ms)
            setTimeout(() => createBIMEXChart(), 500);

            // Graphique 4: Classification (après 600ms)
            setTimeout(() => createClassificationChart(), 700);
        }

        // 📊 FONCTION: Créer le graphique des anomalies
        function createAnomaliesChart() {
            if (anomaliesCanvas && anomaliesData) {
                const anomaliesCtx = anomaliesCanvas.getContext('2d');
                new Chart(anomaliesCtx, {
                    type: 'doughnut',
                    data: anomaliesData,
                    options: {
                        // 🚀 OPTIMISATIONS PERFORMANCE
                        responsive: false,
                        animation: {
                            duration: 0  // Pas d'animation
                        },
                        interaction: {
                            intersect: false,
                            mode: 'nearest'
                        },
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    usePointStyle: true,
                                    padding: 8
                                }
                            },
                            title: {
                                display: true,
                                text: '📊 Répartition des Anomalies',
                                font: { size: 12 }
                            }
                        },
                        elements: {
                            arc: {
                                borderWidth: 1
                            }
                        }
                    }
                });
                console.log("✅ Graphique anomalies créé (optimisé)");
            } else {
                console.error("❌ Impossible de créer le graphique anomalies");
            }
        }

        // ♿ FONCTION: Créer le graphique PMR
        function createPMRChart() {
            if (pmrCanvas && pmrData) {
                const pmrCtx = pmrCanvas.getContext('2d');
                new Chart(pmrCtx, {
                    type: 'pie',
                    data: pmrData,
                    options: {
                        // 🚀 OPTIMISATIONS PERFORMANCE
                        responsive: false,
                        animation: {
                            duration: 0  // Pas d'animation
                        },
                        interaction: {
                            intersect: false,
                            mode: 'nearest'
                        },
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    usePointStyle: true,
                                    padding: 8
                                }
                            },
                            title: {
                                display: true,
                                text: '♿ Analyse PMR',
                                font: { size: 12 }
                            }
                        },
                        elements: {
                            arc: {
                                borderWidth: 1
                            }
                        }
                    }
                });
                console.log("✅ Graphique PMR créé (optimisé)");
            } else {
                console.error("❌ Impossible de créer le graphique PMR");
            }
        }

        // 🎯 FONCTION: Créer le graphique BIMEX
        function createBIMEXChart() {
            if (bimexCanvas && anomaliesData) {
                const bimexCtx = bimexCanvas.getContext('2d');
                const bimexData = {
                    labels: ['Critique', 'Élevée', 'Moyenne', 'Faible'],
                    datasets: [{
                        data: anomaliesData.datasets[0].data,
                        backgroundColor: ['#DC2626', '#EF4444', '#F59E0B', '#10B981'],
                        borderWidth: 1,  // Réduit pour performance
                        borderColor: '#ffffff'
                    }]
                };

                new Chart(bimexCtx, {
                    type: 'bar',
                    data: bimexData,
                    options: {
                        // 🚀 OPTIMISATIONS PERFORMANCE
                        responsive: false,
                        animation: {
                            duration: 0  // Pas d'animation
                        },
                        interaction: {
                            intersect: false,
                            mode: 'nearest'
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: '🎯 ANALYSE BIMEX - RÉPARTITION DES ANOMALIES',
                                font: { size: 12 }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: false  // Masquer la grille pour performance
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        elements: {
                            bar: {
                                borderWidth: 1
                            }
                        }
                    }
                });
                console.log("✅ Graphique BIMEX créé (optimisé)");
            } else {
                console.error("❌ Impossible de créer le graphique BIMEX");
            }
        }

        // Fonction pour afficher le popup de téléchargement
        function showDownloadPopup() {
            // Supprimer tout popup existant
            const existingPopup = document.getElementById('downloadPopup');
            if (existingPopup) {
                existingPopup.remove();
            }

            const popup = document.createElement('div');
            popup.id = 'downloadPopup';
            popup.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 40px;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                max-width: 400px;
                width: 90%;
            `;

            content.innerHTML = `
                <style>
                    @keyframes downloadSpin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    .download-spinner {
                        border: 4px solid #f3f4f6;
                        border-top: 4px solid #3b82f6;
                        border-radius: 50%;
                        width: 50px;
                        height: 50px;
                        animation: downloadSpin 1s linear infinite;
                        margin: 0 auto 20px;
                    }
                </style>
                <div class="download-spinner"></div>
                <h3 style="color: #1e40af; margin: 0 0 10px 0; font-size: 20px;">📄 Téléchargement</h3>
                <p style="color: #6b7280; margin: 10px 0 0 0; font-size: 14px;">
                    Génération du PDF en cours...
                </p>
            `;

            popup.appendChild(content);
            document.body.appendChild(popup);
        }

        // Fonction pour masquer le popup de téléchargement
        function hideDownloadPopup() {
            const popup = document.getElementById('downloadPopup');
            if (popup) {
                popup.remove();
            }
        }

        // Fonction de téléchargement PDF améliorée
        async function downloadPDF() {
            // Afficher le popup de chargement
            showDownloadPopup();

            try {
                // Essayer d'abord l'API de conversion
                const response = await fetch(`/api/download-pdf/{{ report_id }}`);

                if (response.ok && response.headers.get('content-type') === 'application/pdf') {
                    // Si c'est un PDF, le télécharger
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'rapport_bim.pdf';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    // Fermer le popup après un court délai
                    setTimeout(() => {
                        hideDownloadPopup();
                    }, 1000);
                } else {
                    // Si échec, proposer l'impression navigateur
                    throw new Error('Conversion PDF échouée');
                }
            } catch (error) {
                console.log('Fallback vers impression navigateur');

                // Fermer le popup de téléchargement
                hideDownloadPopup();

                // Afficher un message et ouvrir l'impression
                if (confirm('⚠️ Conversion PDF automatique indisponible.\n\n✅ Voulez-vous utiliser l\'impression du navigateur ?\n(Choisissez "Enregistrer au format PDF" dans les options)')) {
                    window.print();
                }
            }
        }

        // 🏢 FONCTION: Créer le graphique Classification IA
        function createClassificationChart() {
            const classificationCanvas = document.getElementById('classificationChart');
            if (classificationCanvas) {
                const confidence = {{ building_confidence | default("85.0") }};
                const classificationCtx = classificationCanvas.getContext('2d');

                new Chart(classificationCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Confiance IA', 'Incertitude'],
                        datasets: [{
                            data: [confidence, 100 - confidence],
                            backgroundColor: ['#3b82f6', '#e5e7eb'],
                            borderWidth: 1,  // Réduit pour performance
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        // 🚀 OPTIMISATIONS PERFORMANCE
                        responsive: false,  // Désactiver responsive pour éviter les recalculs
                        maintainAspectRatio: true,
                        animation: {
                            duration: 0  // Désactiver les animations pour éviter requestAnimationFrame
                        },
                        interaction: {
                            intersect: false,
                            mode: 'nearest'
                        },
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    font: { size: 10 },
                                    usePointStyle: true,  // Plus léger
                                    padding: 8
                                }
                            },
                            title: {
                                display: true,
                                text: '🏢 Classification IA',
                                font: { size: 12 }
                            }
                        },
                        cutout: '60%',
                        // 🎯 Optimisations supplémentaires
                        elements: {
                            arc: {
                                borderWidth: 1  // Réduire la largeur des bordures
                            }
                        }
                    }
                });
                console.log("✅ Graphique Classification créé (optimisé)");
            } else {
                console.error("❌ Impossible de créer le graphique Classification");
            }
        }

        // 🚀 DÉMARRAGE OPTIMISÉ - Chargement séquentiel des graphiques
        window.addEventListener('load', function() {
            console.log("🎯 Page chargée - Démarrage des graphiques optimisés");

            // Démarrer le chargement séquentiel des graphiques
            createChartsSequentially();

            // Animation des barres de progression (après les graphiques)
            setTimeout(() => {
                const progressBars = document.querySelectorAll('.progress-fill');
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 200);
                });
            }, 1000);  // Attendre que les graphiques soient créés

            // 📊 Signal pour Playwright que la page est prête
            setTimeout(() => {
                document.body.setAttribute('data-charts-loaded', 'true');
                console.log("✅ Tous les graphiques sont chargés - Prêt pour PDF");
            }, 2000);
        });

        // Fonction pour retourner intelligemment à l'analyse
        function goBackToAnalysis() {
            // Essayer de récupérer les paramètres du projet depuis l'URL ou les données
            const urlParams = new URLSearchParams(window.location.search);
            const project = urlParams.get('project') || '{{ project_id | default("") }}';

            if (project) {
                // Retourner à l'analyse avec les paramètres du projet
                window.location.href = `/analysis?project=${project}&auto=true&file_detected=true&step=detailed`;
            } else {
                // Fallback vers la page d'analyse générale
                window.location.href = '/analysis';
            }
        }
    </script>
</body>
</html>
