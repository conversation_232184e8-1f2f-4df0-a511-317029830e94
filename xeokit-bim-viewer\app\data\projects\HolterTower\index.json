{"id": "holterTower", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": [{"id": "ifcCXConverterPipeline1", "name": "Holter Tower", "backfaces": false, "manifest": "manifest.json"}], "viewerConfigs": {"backgroundColor": [0.95, 0.95, 1.0]}, "viewerContent": {"modelsLoaded": ["ifcCXConverterPipeline1"]}, "viewerState": {"tabOpen": "classes", "expandObjectsTree": 3, "expandClassesTree": 1, "expandStoreysTree": 1, "saoEnabled": "true", "objectColorSource": "viewer"}}