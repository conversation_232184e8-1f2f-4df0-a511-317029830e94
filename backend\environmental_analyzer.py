"""
🌱 BIMEX - Analyseur Environnemental & Durabilité
Calcul automatique de l'empreinte carbone et analyse de durabilité
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import json
import ifcopenshell
import ifcopenshell.util.element
import ifcopenshell.util.unit
import math

logger = logging.getLogger(__name__)

@dataclass
class EnvironmentalImpact:
    """Structure pour l'impact environnemental"""
    category: str
    co2_emissions: float  # kg CO2 eq
    energy_consumption: float  # kWh
    water_usage: float  # litres
    waste_generation: float  # kg
    recyclability_score: float  # 0-10
    sustainability_rating: str  # A+, A, B, C, D, E, F

@dataclass
class SustainabilityRecommendation:
    """Structure pour les recommandations de durabilité"""
    category: str
    recommendation: str
    potential_co2_reduction: float
    potential_cost_savings: float
    implementation_difficulty: str  # Easy, Medium, Hard
    payback_period: float  # années

class EnvironmentalAnalyzer:
    """
    🌱 Analyseur Environnemental & Durabilité
    Calcule l'empreinte carbone et propose des optimisations
    """
    
    def __init__(self, ifc_file_path: str):
        """
        Initialise l'analyseur environnemental
        
        Args:
            ifc_file_path: Chemin vers le fichier IFC
        """
        self.ifc_file_path = ifc_file_path
        self.ifc_file = ifcopenshell.open(ifc_file_path)
        self.environmental_impacts = []
        self.total_co2_emissions = 0.0
        
        # Base de données des impacts environnementaux (kg CO2 eq / unité)
        self.material_impacts = {
            "Concrete": {"co2_per_m3": 315.0, "energy_per_m3": 120.0, "recyclability": 6.0},
            "Steel": {"co2_per_m3": 2100.0, "energy_per_m3": 2800.0, "recyclability": 9.0},
            "Wood": {"co2_per_m3": -500.0, "energy_per_m3": 50.0, "recyclability": 8.5},  # Négatif = stockage carbone
            "Brick": {"co2_per_m3": 240.0, "energy_per_m3": 180.0, "recyclability": 7.0},
            "Glass": {"co2_per_m3": 850.0, "energy_per_m3": 1200.0, "recyclability": 8.0},
            "Aluminum": {"co2_per_m3": 8200.0, "energy_per_m3": 15000.0, "recyclability": 9.5},
            "Insulation": {"co2_per_m3": 45.0, "energy_per_m3": 80.0, "recyclability": 4.0},
            "Plaster": {"co2_per_m3": 120.0, "energy_per_m3": 90.0, "recyclability": 5.0},
            "Default": {"co2_per_m3": 200.0, "energy_per_m3": 150.0, "recyclability": 6.0}
        }
        
        # Facteurs de performance énergétique
        self.energy_factors = {
            "heating_demand": 80.0,  # kWh/m²/an
            "cooling_demand": 25.0,  # kWh/m²/an
            "lighting_demand": 15.0,  # kWh/m²/an
            "ventilation_demand": 10.0  # kWh/m²/an
        }
        
        logger.info(f"Analyseur environnemental initialisé pour: {ifc_file_path}")
    
    def analyze_environmental_impact(self) -> Dict[str, Any]:
        """
        🌱 Analyse complète de l'impact environnemental
        
        Returns:
            Dictionnaire avec toutes les analyses environnementales
        """
        try:
            logger.info("🌱 Début de l'analyse environnementale...")
            
            # Réinitialiser les impacts
            self.environmental_impacts = []
            self.total_co2_emissions = 0.0
            
            # 1. 🏭 Empreinte carbone des matériaux
            materials_impact = self._calculate_materials_carbon_footprint()
            
            # 2. ⚡ Analyse énergétique du bâtiment
            energy_analysis = self._analyze_building_energy_performance()
            
            # 3. 💧 Analyse de la consommation d'eau
            water_analysis = self._analyze_water_consumption()
            
            # 4. ♻️ Analyse de recyclabilité
            recyclability_analysis = self._analyze_recyclability()
            
            # 5. 🌡️ Analyse du confort thermique
            thermal_comfort = self._analyze_thermal_comfort()
            
            # 6. 🌞 Potentiel d'énergie renouvelable
            renewable_potential = self._analyze_renewable_energy_potential()
            
            # 7. 📊 Score de durabilité global
            sustainability_score = self._calculate_sustainability_score()
            
            # 8. 🎯 Recommandations d'optimisation
            optimization_recommendations = self._generate_environmental_recommendations()
            
            # 9. 📈 Comparaison avec les standards
            standards_comparison = self._compare_with_standards()
            
            logger.info(f"✅ Analyse environnementale terminée: {self.total_co2_emissions:.2f} kg CO2 eq")
            
            return {
                "total_co2_emissions": self.total_co2_emissions,
                "sustainability_score": sustainability_score,
                "environmental_rating": self._get_environmental_rating(sustainability_score),
                "materials_impact": materials_impact,
                "energy_analysis": energy_analysis,
                "water_analysis": water_analysis,
                "recyclability_analysis": recyclability_analysis,
                "thermal_comfort": thermal_comfort,
                "renewable_potential": renewable_potential,
                "optimization_recommendations": optimization_recommendations,
                "standards_comparison": standards_comparison,
                "environmental_impacts": [impact.__dict__ for impact in self.environmental_impacts],
                "analysis_timestamp": datetime.now().isoformat(),
                "certification_potential": self._assess_certification_potential()
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de l'analyse environnementale: {e}")
            raise
    
    def _calculate_materials_carbon_footprint(self) -> Dict[str, Any]:
        """🏭 Calcul de l'empreinte carbone des matériaux"""
        try:
            logger.info("🏭 Calcul de l'empreinte carbone des matériaux...")
            
            materials_footprint = {}
            total_materials_co2 = 0.0
            total_embodied_energy = 0.0
            
            # Analyser tous les matériaux
            materials = self.ifc_file.by_type("IfcMaterial")
            
            for material in materials:
                material_name = material.Name or "Unknown"
                
                # Obtenir les données d'impact
                impact_data = self._get_material_impact(material_name)
                
                # Estimer la quantité
                quantity = self._estimate_material_quantity(material)
                
                # Calculer l'impact
                co2_impact = quantity * impact_data["co2_per_m3"]
                energy_impact = quantity * impact_data["energy_per_m3"]
                
                total_materials_co2 += co2_impact
                total_embodied_energy += energy_impact
                
                materials_footprint[material_name] = {
                    "quantity_m3": quantity,
                    "co2_emissions": co2_impact,
                    "embodied_energy": energy_impact,
                    "recyclability_score": impact_data["recyclability"]
                }
                
                # Ajouter à la liste des impacts
                self.environmental_impacts.append(EnvironmentalImpact(
                    category="Matériaux",
                    co2_emissions=co2_impact,
                    energy_consumption=energy_impact,
                    water_usage=quantity * 10,  # Estimation
                    waste_generation=quantity * 0.05,  # 5% de déchets
                    recyclability_score=impact_data["recyclability"],
                    sustainability_rating=self._get_material_rating(impact_data["co2_per_m3"])
                ))
            
            self.total_co2_emissions += total_materials_co2
            
            return {
                "total_co2_emissions": total_materials_co2,
                "total_embodied_energy": total_embodied_energy,
                "materials_breakdown": materials_footprint,
                "average_recyclability": self._calculate_average_recyclability(materials_footprint)
            }
            
        except Exception as e:
            logger.error(f"Erreur calcul empreinte carbone: {e}")
            return {"total_co2_emissions": 0.0, "error": str(e)}
    
    def _analyze_building_energy_performance(self) -> Dict[str, Any]:
        """⚡ Analyse de la performance énergétique"""
        try:
            logger.info("⚡ Analyse de la performance énergétique...")
            
            # Estimer la surface totale
            total_floor_area = self._estimate_total_floor_area()
            
            # Calculer les besoins énergétiques
            heating_demand = total_floor_area * self.energy_factors["heating_demand"]
            cooling_demand = total_floor_area * self.energy_factors["cooling_demand"]
            lighting_demand = total_floor_area * self.energy_factors["lighting_demand"]
            ventilation_demand = total_floor_area * self.energy_factors["ventilation_demand"]
            
            total_energy_demand = heating_demand + cooling_demand + lighting_demand + ventilation_demand
            
            # Calculer les émissions CO2 liées à l'énergie (0.5 kg CO2/kWh en moyenne)
            energy_co2_emissions = total_energy_demand * 0.5
            self.total_co2_emissions += energy_co2_emissions
            
            # Analyser l'efficacité de l'enveloppe
            envelope_efficiency = self._analyze_building_envelope()
            
            return {
                "total_energy_demand": total_energy_demand,
                "energy_breakdown": {
                    "heating": heating_demand,
                    "cooling": cooling_demand,
                    "lighting": lighting_demand,
                    "ventilation": ventilation_demand
                },
                "energy_co2_emissions": energy_co2_emissions,
                "energy_intensity": total_energy_demand / total_floor_area if total_floor_area > 0 else 0,
                "envelope_efficiency": envelope_efficiency,
                "energy_class": self._get_energy_class(total_energy_demand / total_floor_area if total_floor_area > 0 else 0)
            }
            
        except Exception as e:
            logger.error(f"Erreur analyse énergétique: {e}")
            return {"total_energy_demand": 0.0, "error": str(e)}
    
    def _analyze_water_consumption(self) -> Dict[str, Any]:
        """💧 Analyse de la consommation d'eau"""
        try:
            logger.info("💧 Analyse de la consommation d'eau...")
            
            # Estimer la consommation d'eau basée sur les espaces
            spaces = self.ifc_file.by_type("IfcSpace")
            total_water_consumption = 0.0
            
            for space in spaces:
                space_name = space.Name or "Unknown"
                
                # Facteurs de consommation par type d'espace (L/m²/an)
                if "bathroom" in space_name.lower() or "toilet" in space_name.lower():
                    water_factor = 500.0
                elif "kitchen" in space_name.lower():
                    water_factor = 300.0
                elif "office" in space_name.lower():
                    water_factor = 50.0
                else:
                    water_factor = 25.0
                
                # Estimer la surface de l'espace
                space_area = self._estimate_space_area(space)
                space_water_consumption = space_area * water_factor
                total_water_consumption += space_water_consumption
            
            return {
                "total_water_consumption": total_water_consumption,
                "water_intensity": total_water_consumption / self._estimate_total_floor_area() if self._estimate_total_floor_area() > 0 else 0,
                "rainwater_harvesting_potential": self._calculate_rainwater_potential(),
                "greywater_recycling_potential": total_water_consumption * 0.3
            }

        except Exception as e:
            logger.error(f"Erreur analyse eau: {e}")
            return {"total_water_consumption": 0.0, "error": str(e)}

    def _analyze_recyclability(self) -> Dict[str, Any]:
        """♻️ Analyse de recyclabilité"""
        try:
            logger.info("♻️ Analyse de recyclabilité...")

            materials = self.ifc_file.by_type("IfcMaterial")
            total_recyclability_score = 0.0
            recyclable_materials = []

            for material in materials:
                material_name = material.Name or "Unknown"
                impact_data = self._get_material_impact(material_name)
                recyclability = impact_data["recyclability"]

                total_recyclability_score += recyclability

                if recyclability >= 8.0:
                    recyclable_materials.append({
                        "material": material_name,
                        "recyclability_score": recyclability,
                        "category": "Excellent"
                    })
                elif recyclability >= 6.0:
                    recyclable_materials.append({
                        "material": material_name,
                        "recyclability_score": recyclability,
                        "category": "Good"
                    })

            average_recyclability = total_recyclability_score / len(materials) if materials else 0.0

            return {
                "average_recyclability_score": average_recyclability,
                "recyclable_materials": recyclable_materials,
                "waste_reduction_potential": self._calculate_waste_reduction_potential(),
                "circular_economy_score": self._calculate_circular_economy_score(average_recyclability)
            }

        except Exception as e:
            logger.error(f"Erreur analyse recyclabilité: {e}")
            return {"average_recyclability_score": 0.0, "error": str(e)}

    def _analyze_thermal_comfort(self) -> Dict[str, Any]:
        """🌡️ Analyse du confort thermique"""
        try:
            logger.info("🌡️ Analyse du confort thermique...")

            # Analyser l'orientation du bâtiment
            building_orientation = self._analyze_building_orientation()

            # Analyser les ouvertures
            windows = self.ifc_file.by_type("IfcWindow")
            window_to_wall_ratio = len(windows) / max(len(self.ifc_file.by_type("IfcWall")), 1)

            # Calculer le score de confort thermique
            thermal_comfort_score = self._calculate_thermal_comfort_score(building_orientation, window_to_wall_ratio)

            return {
                "thermal_comfort_score": thermal_comfort_score,
                "building_orientation": building_orientation,
                "window_to_wall_ratio": window_to_wall_ratio,
                "natural_ventilation_potential": self._assess_natural_ventilation(),
                "thermal_mass_analysis": self._analyze_thermal_mass()
            }

        except Exception as e:
            logger.error(f"Erreur analyse confort thermique: {e}")
            return {"thermal_comfort_score": 0.0, "error": str(e)}

    def _analyze_renewable_energy_potential(self) -> Dict[str, Any]:
        """🌞 Analyse du potentiel d'énergie renouvelable"""
        try:
            logger.info("🌞 Analyse du potentiel d'énergie renouvelable...")

            # Estimer la surface de toiture
            roof_area = self._estimate_roof_area()

            # Potentiel solaire photovoltaïque
            solar_pv_potential = roof_area * 150  # kWh/m²/an

            # Potentiel solaire thermique
            solar_thermal_potential = roof_area * 400  # kWh/m²/an

            # Potentiel géothermique (basé sur la surface au sol)
            ground_area = self._estimate_ground_area()
            geothermal_potential = ground_area * 50  # kWh/m²/an

            return {
                "solar_pv_potential": solar_pv_potential,
                "solar_thermal_potential": solar_thermal_potential,
                "geothermal_potential": geothermal_potential,
                "total_renewable_potential": solar_pv_potential + solar_thermal_potential + geothermal_potential,
                "renewable_coverage_ratio": self._calculate_renewable_coverage_ratio(solar_pv_potential + geothermal_potential)
            }

        except Exception as e:
            logger.error(f"Erreur analyse énergies renouvelables: {e}")
            return {"total_renewable_potential": 0.0, "error": str(e)}

    def _calculate_sustainability_score(self) -> float:
        """📊 Calcul du score de durabilité global"""
        try:
            # Pondération des différents critères
            weights = {
                "carbon_footprint": 0.3,
                "energy_efficiency": 0.25,
                "recyclability": 0.2,
                "water_efficiency": 0.15,
                "renewable_potential": 0.1
            }

            # Normaliser les scores (0-10)
            carbon_score = max(0, 10 - (self.total_co2_emissions / 1000))  # Normalisation approximative
            energy_score = 7.0  # Score par défaut
            recyclability_score = self._calculate_average_recyclability({})
            water_score = 6.0  # Score par défaut
            renewable_score = 5.0  # Score par défaut

            # Calculer le score pondéré
            sustainability_score = (
                carbon_score * weights["carbon_footprint"] +
                energy_score * weights["energy_efficiency"] +
                recyclability_score * weights["recyclability"] +
                water_score * weights["water_efficiency"] +
                renewable_score * weights["renewable_potential"]
            )

            return min(10.0, max(0.0, sustainability_score))

        except Exception as e:
            logger.error(f"Erreur calcul score durabilité: {e}")
            return 5.0

    def _generate_environmental_recommendations(self) -> List[Dict[str, Any]]:
        """🎯 Génération des recommandations environnementales"""
        recommendations = []

        # Recommandations basées sur l'analyse
        if self.total_co2_emissions > 10000:
            recommendations.append({
                "category": "Réduction Carbone",
                "recommendation": "Remplacer le béton par des matériaux bas carbone",
                "potential_co2_reduction": self.total_co2_emissions * 0.2,
                "implementation_difficulty": "Medium",
                "payback_period": 5.0
            })

        recommendations.extend([
            {
                "category": "Efficacité Énergétique",
                "recommendation": "Améliorer l'isolation thermique",
                "potential_co2_reduction": 500.0,
                "implementation_difficulty": "Easy",
                "payback_period": 3.0
            },
            {
                "category": "Énergies Renouvelables",
                "recommendation": "Installer des panneaux solaires",
                "potential_co2_reduction": 1000.0,
                "implementation_difficulty": "Medium",
                "payback_period": 8.0
            },
            {
                "category": "Gestion de l'Eau",
                "recommendation": "Système de récupération d'eau de pluie",
                "potential_co2_reduction": 100.0,
                "implementation_difficulty": "Easy",
                "payback_period": 4.0
            }
        ])

        return recommendations

    def _compare_with_standards(self) -> Dict[str, Any]:
        """📈 Comparaison avec les standards environnementaux"""
        return {
            "RT2012_compliance": self._check_rt2012_compliance(),
            "RE2020_compliance": self._check_re2020_compliance(),
            "BREEAM_potential": self._assess_breeam_potential(),
            "HQE_potential": self._assess_hqe_potential(),
            "LEED_potential": self._assess_leed_potential()
        }

    # Méthodes utilitaires
    def _get_material_impact(self, material_name: str) -> Dict[str, float]:
        """Obtenir l'impact environnemental d'un matériau"""
        for key, impact_data in self.material_impacts.items():
            if key.lower() in material_name.lower():
                return impact_data
        return self.material_impacts["Default"]

    def _estimate_material_quantity(self, material) -> float:
        """Estimer la quantité d'un matériau"""
        return np.random.uniform(10, 100)  # Simulation

    def _estimate_total_floor_area(self) -> float:
        """Estimer la surface totale du plancher"""
        slabs = self.ifc_file.by_type("IfcSlab")
        return len(slabs) * 100.0 if slabs else 1000.0  # Simulation

    def _estimate_space_area(self, space) -> float:
        """Estimer la surface d'un espace"""
        return np.random.uniform(10, 50)  # Simulation

    def _get_environmental_rating(self, score: float) -> str:
        """Obtenir la classe environnementale"""
        if score >= 9.0: return "A+"
        elif score >= 8.0: return "A"
        elif score >= 7.0: return "B"
        elif score >= 6.0: return "C"
        elif score >= 5.0: return "D"
        elif score >= 4.0: return "E"
        else: return "F"

    def _get_material_rating(self, co2_per_m3: float) -> str:
        """Obtenir la classe d'un matériau"""
        if co2_per_m3 < 100: return "A"
        elif co2_per_m3 < 300: return "B"
        elif co2_per_m3 < 500: return "C"
        elif co2_per_m3 < 1000: return "D"
        else: return "E"

    def _calculate_average_recyclability(self, materials_footprint: Dict) -> float:
        """Calculer la recyclabilité moyenne"""
        if not materials_footprint:
            return 6.0  # Valeur par défaut

        total_recyclability = sum(data.get("recyclability_score", 6.0) for data in materials_footprint.values())
        return total_recyclability / len(materials_footprint)

    def _analyze_building_envelope(self) -> Dict[str, Any]:
        """Analyser l'efficacité de l'enveloppe"""
        walls = self.ifc_file.by_type("IfcWall")
        windows = self.ifc_file.by_type("IfcWindow")

        return {
            "wall_count": len(walls),
            "window_count": len(windows),
            "envelope_efficiency_score": 7.0  # Score par défaut
        }

    def _get_energy_class(self, energy_intensity: float) -> str:
        """Obtenir la classe énergétique"""
        if energy_intensity < 50: return "A"
        elif energy_intensity < 90: return "B"
        elif energy_intensity < 150: return "C"
        elif energy_intensity < 230: return "D"
        elif energy_intensity < 330: return "E"
        elif energy_intensity < 450: return "F"
        else: return "G"

    # Méthodes de simulation pour les fonctionnalités avancées
    def _calculate_rainwater_potential(self) -> float:
        """Calculer le potentiel de récupération d'eau de pluie"""
        roof_area = self._estimate_roof_area()
        annual_rainfall = 600  # mm/an (moyenne France)
        return roof_area * annual_rainfall * 0.8  # 80% d'efficacité

    def _estimate_roof_area(self) -> float:
        """Estimer la surface de toiture"""
        roofs = self.ifc_file.by_type("IfcRoof")
        return len(roofs) * 200.0 if roofs else 500.0  # Simulation

    def _estimate_ground_area(self) -> float:
        """Estimer la surface au sol"""
        return self._estimate_total_floor_area()  # Approximation

    def _calculate_renewable_coverage_ratio(self, renewable_potential: float) -> float:
        """Calculer le ratio de couverture par les énergies renouvelables"""
        total_energy_demand = self._estimate_total_floor_area() * 130  # kWh/m²/an
        return min(1.0, renewable_potential / total_energy_demand) if total_energy_demand > 0 else 0.0

    # Méthodes de conformité aux standards
    def _check_rt2012_compliance(self) -> Dict[str, Any]:
        """Vérifier la conformité RT2012"""
        return {"compliant": True, "score": 8.0, "requirements_met": ["Isolation", "Étanchéité"]}

    def _check_re2020_compliance(self) -> Dict[str, Any]:
        """Vérifier la conformité RE2020"""
        return {"compliant": False, "score": 6.0, "missing_requirements": ["Empreinte carbone"]}

    def _assess_breeam_potential(self) -> Dict[str, Any]:
        """Évaluer le potentiel BREEAM"""
        return {"potential_rating": "Good", "score": 7.0}

    def _assess_hqe_potential(self) -> Dict[str, Any]:
        """Évaluer le potentiel HQE"""
        return {"potential_rating": "Bon", "score": 7.5}

    def _assess_leed_potential(self) -> Dict[str, Any]:
        """Évaluer le potentiel LEED"""
        return {"potential_rating": "Silver", "score": 6.5}

    def _assess_certification_potential(self) -> Dict[str, Any]:
        """Évaluer le potentiel de certification"""
        return {
            "recommended_certifications": ["HQE", "BREEAM"],
            "certification_readiness": 0.7,
            "estimated_certification_cost": 15000.0
        }

    def _calculate_waste_reduction_potential(self) -> float:
        """Calculer le potentiel de réduction des déchets"""
        try:
            materials = self.ifc_file.by_type("IfcMaterial")
            total_potential = 0.0

            for material in materials:
                material_name = getattr(material, 'Name', 'Unknown')
                # Estimer le potentiel de réduction basé sur le type de matériau
                if 'concrete' in material_name.lower() or 'béton' in material_name.lower():
                    total_potential += 0.15  # 15% de réduction possible
                elif 'steel' in material_name.lower() or 'acier' in material_name.lower():
                    total_potential += 0.25  # 25% de réduction possible
                elif 'wood' in material_name.lower() or 'bois' in material_name.lower():
                    total_potential += 0.30  # 30% de réduction possible
                else:
                    total_potential += 0.10  # 10% par défaut

            return min(total_potential / len(materials) if materials else 0.0, 0.40)  # Max 40%
        except Exception as e:
            logger.error(f"Erreur calcul potentiel réduction déchets: {e}")
            return 0.20  # Valeur par défaut

    def _analyze_building_orientation(self) -> Dict[str, Any]:
        """Analyser l'orientation du bâtiment"""
        try:
            # Analyser les murs pour déterminer l'orientation
            walls = self.ifc_file.by_type("IfcWall")

            # Simulation d'analyse d'orientation
            orientations = {
                "north": len([w for w in walls[:len(walls)//4]]),
                "south": len([w for w in walls[len(walls)//4:len(walls)//2]]),
                "east": len([w for w in walls[len(walls)//2:3*len(walls)//4]]),
                "west": len([w for w in walls[3*len(walls)//4:]])
            }

            optimal_orientation = max(orientations, key=orientations.get)

            return {
                "primary_orientation": optimal_orientation,
                "solar_gain_potential": 0.75 if optimal_orientation in ['south', 'southeast'] else 0.60,
                "natural_lighting_score": 8.2,
                "thermal_efficiency": 0.68
            }
        except Exception as e:
            logger.error(f"Erreur analyse orientation: {e}")
            return {
                "primary_orientation": "south",
                "solar_gain_potential": 0.70,
                "natural_lighting_score": 7.5,
                "thermal_efficiency": 0.65
            }
